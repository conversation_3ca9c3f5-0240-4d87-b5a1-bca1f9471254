import { createApp } from 'vue'
import { createPinia } from 'pinia'

// Element Plus - 先导入Element Plus的CSS
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

// 导入全局样式 - 在Element Plus之后导入，这样可以覆盖默认样式
import './styles/index.scss'

// 导入代码高亮样式
import 'highlight.js/styles/github-dark.css'

// 应用组件
import App from './App.vue'
import router from './router'

// 创建应用实例
const app = createApp(App)

// 创建Pinia实例
const pinia = createPinia()

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('全局错误捕获:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)

  // 可以在这里添加错误上报逻辑
  // reportError(error, info)
}

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('全局警告:', msg)
  console.warn('组件实例:', instance)
  console.warn('组件追踪:', trace)
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 显示加载器
function showLoader() {
  const loaderDiv = document.createElement('div')
  loaderDiv.id = 'app-loader'
  loaderDiv.innerHTML = `
    <div style="
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    ">
      <div style="text-align: center;">
        <div style="
          width: 64px;
          height: 64px;
          margin: 0 auto 2rem;
          border: 4px solid #e2e8f0;
          border-top: 4px solid #667eea;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        "></div>
        <h2 style="
          font-size: 1.75rem;
          font-weight: 700;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          margin-bottom: 0.5rem;
        ">AI创作助手平台</h2>
        <p style="color: #64748b;">正在初始化应用...</p>
      </div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  `
  document.body.appendChild(loaderDiv)
}

// 隐藏加载器
function hideLoader() {
  const loader = document.getElementById('app-loader')
  if (loader) {
    loader.remove()
  }
}

// 简化的应用初始化函数
async function initializeApp() {
  try {
    console.log('🚀 开始初始化应用...')

    // 显示加载器
    showLoader()

    // 初始化stores
    try {
      const { initStores } = await import('./stores/index.js')
      await initStores()
      console.log('✅ Stores初始化成功')
    } catch (error) {
      console.warn('⚠️ Stores初始化失败，继续启动应用:', error)
    }

    // 直接挂载应用
    const appInstance = app.mount('#app')
    console.log('✅ 应用挂载成功')

    // 等待下一个tick确保DOM完全渲染
    await nextTick()

    // 隐藏加载器
    hideLoader()

    console.log('🎉 应用初始化完成')
    return appInstance
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)

    // 隐藏加载器
    hideLoader()

    // 显示错误信息给用户
    const errorDiv = document.createElement('div')
    errorDiv.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: #fff;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        text-align: center;
        z-index: 9999;
        max-width: 400px;
      ">
        <h3 style="color: #ef4444; margin-bottom: 1rem;">应用初始化失败</h3>
        <p style="color: #6b7280; margin-bottom: 1.5rem;">请刷新页面重试，如果问题持续存在，请联系技术支持。</p>
        <details style="text-align: left; margin-bottom: 1rem;">
          <summary style="cursor: pointer;">错误详情</summary>
          <pre style="font-size: 0.8rem; color: #ef4444; margin-top: 0.5rem;">${error.message}</pre>
        </details>
        <button onclick="window.location.reload()" style="
          background: #3b82f6;
          color: white;
          border: none;
          padding: 0.5rem 1rem;
          border-radius: 4px;
          cursor: pointer;
        ">刷新页面</button>
      </div>
    `
    document.body.appendChild(errorDiv)
    throw error
  }
}

// 导入必要的模块
import { nextTick } from 'vue'

// 在开发环境加载调试工具
if (import.meta.env.DEV) {
  import('@/utils/debug.js').then(debug => {
    console.log('🛠️ 调试工具已加载')
  }).catch(error => {
    console.warn('⚠️ 调试工具加载失败:', error)
  })
}

// 全局未捕获错误处理
window.addEventListener('error', (event) => {
  console.error('全局未捕获错误:', event.error)
  console.error('错误文件:', event.filename)
  console.error('错误行号:', event.lineno)
  console.error('错误列号:', event.colno)
})

// 全局未捕获Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('全局未捕获Promise错误:', event.reason)
  event.preventDefault() // 阻止默认的错误处理
})

// 启动应用
initializeApp().catch(error => {
  console.error('应用启动失败:', error)
})
