<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- 移动端侧边栏遮罩层 -->
    <div
      class="sidebar-overlay"
      :class="{ active: !sidebarCollapsed }"
      @click="toggleSidebar"
    ></div>

    <!-- 移动端遮罩层 -->
    <div
      v-if="shouldShowMobileSidebar"
      class="sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <!-- 主布局 -->
    <div class="flex h-screen">
      <!-- 侧边栏 -->
      <aside
        class="bg-white border-r border-gray-200 transition-all duration-300 ease-in-out shadow-soft"
        :class="[
          sidebarCollapsed ? 'w-16' : 'w-80',
          sidebarClasses
        ]"
      >
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between p-4 border-b border-gray-100">
          <button
            @click="toggleSidebar"
            class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 text-gray-600 hover:text-gray-900"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" class="transition-transform duration-200" :class="{ 'rotate-180': sidebarCollapsed }">
              <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
          <h1
            v-if="!sidebarCollapsed"
            class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent animate-fade-in"
          >
            AI 智能对话
          </h1>
        </div>

        <!-- 展开状态的侧边栏内容 -->
        <div v-if="!sidebarCollapsed" class="flex flex-col h-full">
          <!-- 模型选择器 -->
          <div class="p-4 border-b border-gray-100">
            <label class="block text-sm font-medium text-gray-700 mb-2">选择AI模型</label>
            <select
              v-model="selectedModel"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white text-gray-900"
            >
              <!-- Pollinations.AI 免费模型 -->
              <optgroup label="🆓 免费模型 (推荐)">
                <option value="gemini-2.5-flash">Gemini 2.5 Flash (推荐)</option>
                <option value="gemini-2.5-flash-lite">Gemini 2.5 Flash Lite (极速)</option>
                <option value="openai">OpenAI GPT-4o Mini</option>
                <option value="llama">Llama 3.3 70B</option>
                <option value="mistral">Mistral Small 3</option>
                <option value="qwen-coder">Qwen 2.5 Coder (编程)</option>
              </optgroup>
              <!-- GeminiPool 高级模型 -->
              <optgroup label="⭐ 高级模型">
                <option value="gemini-2.5-pro">Gemini 2.5 Pro (最强)</option>
                <option value="gemini-2.0-flash-thinking-exp-1219">Gemini 思维链推理</option>
                <option value="deepseek-reasoning-large">DeepSeek 推理大模型</option>
              </optgroup>
            </select>
          </div>

          <!-- 对话列表 -->
          <div class="flex-1 overflow-y-auto p-4 space-y-2">
            <div class="text-sm font-medium text-gray-500 mb-3">对话历史</div>
            <button
              v-for="conversation in chatStore.conversations"
              :key="conversation.id"
              @click="switchConversation(conversation.id)"
              class="w-full text-left p-3 rounded-lg transition-all duration-200 group"
              :class="conversation.id === chatStore.currentConversationId
                ? 'bg-blue-50 border border-blue-200 text-blue-900 shadow-sm'
                : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'"
            >
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium truncate">{{ conversation.title || '新对话' }}</span>
                <svg
                  v-if="conversation.id === chatStore.currentConversationId"
                  width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-blue-500 flex-shrink-0"
                >
                  <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
              <div class="text-xs text-gray-500 mt-1">
                {{ formatTime(conversation.updatedAt) }}
              </div>
            </button>
          </div>

          <!-- 新建对话按钮 -->
          <div class="p-4 border-t border-gray-100">
            <button
              @click="createNewConversation"
              class="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-medium hover:shadow-strong transform hover:-translate-y-0.5"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              <span class="font-medium">新建对话</span>
            </button>
          </div>
        </div>

        <!-- 折叠状态的侧边栏内容 -->
        <div v-else class="flex flex-col items-center py-4 space-y-4">
          <button
            @click="createNewConversation"
            class="p-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-medium hover:shadow-strong transform hover:-translate-y-0.5"
            title="新建对话"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </aside>

      <!-- 主内容区域 -->
      <main class="flex-1 flex flex-col bg-white">
        <!-- 聊天头部 -->
        <header class="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white/80 backdrop-blur-sm">
          <div class="flex items-center space-x-3">
            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse-gentle"></div>
            <h2 class="text-lg font-semibold text-gray-900">
              {{ chatStore.currentConversation?.title || '新对话' }}
            </h2>
            <span class="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-full">
              {{ selectedModel }}
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <button
              class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="清空对话"
              @click="clearConversation"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M3 6h18M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            <button
              class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              title="设置"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
              </svg>
            </button>
          </div>
        </header>

        <!-- 消息区域 -->
        <section class="flex-1 overflow-hidden" :class="messageListClasses">
          <MessageList
            ref="messageListRef"
            :messages="chatStore.currentMessages"
            :has-messages="chatStore.hasMessages"
            :loading="chatStore.loading"
            :suggested-questions="suggestedQuestions"
            :should-auto-scroll="shouldAutoScroll"
            @send-message="sendMessage"
            @copy-message="copyMessage"
            @regenerate-message="regenerateMessage"
            @load-more="loadMoreMessages"
            @scroll-change="handleScrollChange"
          />
        </section>

        <!-- 输入区域 -->
        <footer class="border-t border-gray-200 bg-white/80 backdrop-blur-sm" :class="chatInputClasses">
          <ChatInput
            ref="chatInputRef"
            :loading="chatStore.loading"
            :disabled="false"
            @send-message="sendMessage"
            @start-voice-input="startVoiceInput"
            @file-upload="handleFileUpload"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
          />
        </footer>
      </main>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useChatStore, useUserStore } from '@/stores'
import { copyToClipboard } from '@/utils/common'
import { marked } from 'marked'
import hljs from 'highlight.js'

// 导入聊天组件
import ChatHeader from '@/components/chat/ChatHeader.vue'
import MessageList from '@/components/chat/MessageList.vue'
import ChatInput from '@/components/chat/ChatInput.vue'

// 导入响应式组合式函数
import { useChatResponsive } from '@/composables/useChatResponsive.js'

const chatStore = useChatStore()
const userStore = useUserStore()

// 使用聊天响应式功能
const {
  sidebarCollapsed,
  sidebarVisible,
  shouldShowMobileSidebar,
  chatInputClasses,
  messageListClasses,
  sidebarClasses,
  toggleSidebar,
  closeSidebar,
  handleInputFocus,
  handleInputBlur,
  scrollToBottom,
  isMobile,
  isTablet
} = useChatResponsive()

// 其他响应式数据
const showSettings = ref(false)
const selectedModel = ref('gemini-2.5-flash')
const voiceModeEnabled = ref(false)
const shouldAutoScroll = ref(true)

// 组件引用
const messageListRef = ref(null)
const chatInputRef = ref(null)

// 建议问题
const suggestedQuestions = [
  '帮我写一个创意故事',
  '解释一下人工智能的原理',
  '推荐一些学习编程的方法',
  '如何提高工作效率？',
  '分析这个问题的解决方案',
  '帮我制定学习计划',
]

// 计算属性
const currentConversationTitle = computed(() => {
  return chatStore.currentConversation?.title || '新对话'
})

// 方法
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

  return date.toLocaleDateString()
}

const handleScrollChange = (scrollInfo) => {
  shouldAutoScroll.value = scrollInfo.isNearBottom
}

const loadMoreMessages = async () => {
  if (chatStore.loading) return
  // 这里可以添加加载更多消息的逻辑
  console.log('加载更多消息...')
}

const clearConversation = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空当前对话吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    // 删除当前对话
    if (chatStore.currentConversationId) {
      await chatStore.deleteConversation(chatStore.currentConversationId)
    }
    ElMessage.success('对话已清空')
  } catch (error) {
    // 用户取消操作
  }
}

const handleFileUpload = (files) => {
  console.log('文件上传:', files)
}

const startVoiceInput = (isRecording) => {
  console.log('语音输入:', isRecording)
}

const sendMessage = async (content) => {
  try {
    await chatStore.sendMessage(content)
    ElMessage.success('消息发送成功')
  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败，请重试')
  }
}

const regenerateMessage = async (messageId) => {
  try {
    // 使用resendMessage方法重新发送
    await chatStore.resendMessage(messageId)
    ElMessage.success('正在重新生成回答...')
  } catch (error) {
    console.error('重新生成失败:', error)
    ElMessage.error('重新生成失败，请重试')
  }
}

const copyMessage = async (content) => {
  try {
    await copyToClipboard(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const createNewConversation = async () => {
  try {
    chatStore.createConversation()
    ElMessage.success('已创建新对话')
  } catch (error) {
    console.error('创建对话失败:', error)
    ElMessage.error('创建对话失败，请重试')
  }
}

const switchConversation = async (conversationId) => {
  try {
    await chatStore.switchConversation(conversationId)
  } catch (error) {
    console.error('切换对话失败:', error)
    ElMessage.error('切换对话失败，请重试')
  }
}

const handleConversationCommand = async (command, conversationId) => {
  try {
    switch (command) {
      case 'delete':
        await ElMessageBox.confirm('确定要删除这个对话吗？', '确认删除', {
          type: 'warning'
        })
        await chatStore.deleteConversation(conversationId)
        ElMessage.success('对话已删除')
        break
      case 'rename':
        ElMessage.info('重命名功能暂未实现')
        break
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }
}

// toggleSidebar 方法现在由 useChatResponsive 提供

const toggleVoiceMode = () => {
  voiceModeEnabled.value = !voiceModeEnabled.value
  ElMessage.info(voiceModeEnabled.value ? '语音模式已开启' : '语音模式已关闭')
}

const exportChat = async () => {
  try {
    const messages = chatStore.currentMessages
    const content = messages.map(msg => `${msg.role}: ${msg.content}`).join('\n\n')
    
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chat-${Date.now()}.txt`
    a.click()
    URL.revokeObjectURL(url)
    
    ElMessage.success('聊天记录已导出')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败，请重试')
  }
}

const clearCurrentChat = async () => {
  try {
    await ElMessageBox.confirm('确定要清空当前对话吗？', '确认清空', {
      type: 'warning'
    })
    // 删除当前对话
    if (chatStore.currentConversationId) {
      await chatStore.deleteConversation(chatStore.currentConversationId)
    }
    ElMessage.success('对话已清空')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空失败:', error)
      ElMessage.error('清空失败，请重试')
    }
  }
}

// 生命周期
onMounted(async () => {
  try {
    // 确保聊天历史已加载
    await chatStore.loadChatHistory()
    
    marked.setOptions({
      highlight: function(code, lang) {
        if (lang && hljs.getLanguage(lang)) {
          try {
            return hljs.highlight(code, { language: lang }).value
          } catch (err) {
            console.error('代码高亮失败:', err)
          }
        }
        return hljs.highlightAuto(code).value
      },
      breaks: true,
      gfm: true
    })
  } catch (error) {
    console.error('初始化失败:', error)
    ElMessage.error('初始化失败，请刷新页面重试')
  }
})

onUnmounted(() => {
  // 清理资源
})

// 监听消息变化
watch(() => chatStore.currentMessages, () => {
  // 消息变化时的处理
}, { deep: true })
</script>

<style lang="scss" scoped>
/* 移动端适配样式 */
@media (max-width: 768px) {
  .min-h-screen {
    height: 100vh;
    overflow: hidden;
  }

  .flex.h-screen {
    flex-direction: column;
    height: 100vh;
  }

  /* 侧边栏移动端适配 */
  aside {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    width: 280px !important;

    &:not(.w-16) {
      transform: translateX(0);
    }

    &.w-16 {
      transform: translateX(-100%);
    }
  }

  /* 主内容区域 */
  .flex-1 {
    width: 100%;
    margin-left: 0;
    padding: 0;
  }

  /* 聊天区域 */
  .flex.flex-col.h-full {
    height: 100vh;
  }

  /* 消息列表 */
  .flex-1.overflow-y-auto {
    padding: 1rem 0.5rem;
  }

  /* 消息气泡 */
  .max-w-3xl {
    max-width: 100%;
    padding: 0 0.5rem;
  }

  /* 输入区域 */
  .border-t.bg-white {
    padding: 0.75rem 0.5rem;
  }

  .max-w-4xl {
    max-width: 100%;
  }

  /* 输入框 */
  .flex.items-end.space-x-2 {
    gap: 0.5rem;
  }

  .flex-1.min-h-\\[44px\\] {
    min-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 按钮组 */
  .flex.space-x-1 {
    gap: 0.25rem;
  }

  .w-10.h-10 {
    width: 2rem;
    height: 2rem;
  }

  /* 模型选择器 */
  .w-full.px-3.py-2 {
    font-size: 16px; /* 防止iOS缩放 */
  }

  /* 对话历史 */
  .space-y-2 {
    gap: 0.5rem;
  }

  /* 工具栏按钮 */
  .flex.items-center.space-x-2 {
    gap: 0.5rem;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  /* 更小屏幕的优化 */
  .p-4 {
    padding: 0.75rem;
  }

  .p-6 {
    padding: 1rem;
  }

  /* 消息气泡更紧凑 */
  .rounded-2xl {
    border-radius: 1rem;
  }

  .p-4.max-w-\\[85\\%\\] {
    padding: 0.75rem;
    max-width: 90%;
  }

  /* 输入区域更紧凑 */
  .border-t.bg-white {
    padding: 0.5rem;
  }

  /* 按钮更小 */
  .w-10.h-10 {
    width: 1.75rem;
    height: 1.75rem;
  }

  /* 文字大小调整 */
  .text-xl {
    font-size: 1.125rem;
  }

  .text-lg {
    font-size: 1rem;
  }

  /* 侧边栏更窄 */
  aside {
    width: 260px !important;
  }
}

/* 侧边栏遮罩层 */
@media (max-width: 768px) {
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;

    &.active {
      opacity: 1;
      visibility: visible;
    }
  }
}

/* 确保移动端输入框不会被虚拟键盘遮挡 */
@media (max-width: 768px) {
  .chat-input-container {
    position: sticky;
    bottom: 0;
    background: white;
    z-index: 100;
  }

  /* iOS Safari 适配 */
  @supports (-webkit-touch-callout: none) {
    .chat-input-container {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
}

/* 滚动条优化 */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.7);
  }
}

/* 移动端遮罩层 */
.sidebar-overlay {
  display: none;

  @media (max-width: 768px) {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 1;
    visibility: visible;
    transition: all 0.3s ease-in-out;
    cursor: pointer;
  }
}

/* 移动端优化类 */
.mobile-optimized {
  @media (max-width: 768px) {
    .flex-1 {
      padding: 0.5rem;
    }

    .border-t {
      border-top-width: 1px;
    }
  }
}

.touch-device {
  // 触摸设备优化
  .action-btn, .tool-btn {
    min-height: 44px;
    min-width: 44px;
  }
}

.keyboard-visible {
  @media (max-width: 768px) {
    .chat-input {
      padding-bottom: 0.25rem;
    }

    .messages-container {
      padding-bottom: 1rem;
    }
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
</style>
