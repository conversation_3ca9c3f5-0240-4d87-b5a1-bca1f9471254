<template>
  <div class="home-page">
    <!-- 震撼英雄区域 -->
    <section class="hero-section">
      <!-- 动态背景 -->
      <div class="hero-background">
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
        <div class="floating-particles">
          <div class="particle" v-for="i in 20" :key="i" :style="getParticleStyle(i)"></div>
        </div>
      </div>

      <div class="container">
        <div class="hero-content">
          <!-- 新产品标识 -->
          <div class="hero-badge">
            <div class="badge-glow"></div>
            <span class="badge-text">🚀 全新AI创作平台</span>
          </div>

          <!-- 主标题 -->
          <h1 class="hero-title">
            <span class="title-line">释放</span>
            <span class="title-line gradient-text">无限创意</span>
            <span class="title-line">让AI成为您的</span>
            <span class="title-line highlight-text">创作伙伴</span>
          </h1>

          <!-- 副标题 -->
          <p class="hero-subtitle">
            体验前沿AI技术，从智能对话到艺术创作<br>
            一站式创意解决方案，让每个人都能成为创作者
          </p>

          <!-- 价值主张 -->
          <div class="value-propositions">
            <div class="value-item">
              <div class="value-icon">⚡</div>
              <span>秒级响应</span>
            </div>
            <div class="value-item">
              <div class="value-icon">🎨</div>
              <span>专业品质</span>
            </div>
            <div class="value-item">
              <div class="value-icon">🔥</div>
              <span>无限创作</span>
            </div>
          </div>

          <!-- 行动按钮 -->
          <div class="hero-actions">
            <button class="cta-button primary" @click="startCreating">
              <div class="button-content">
                <span>立即开始创作</span>
                <div class="button-icon">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                    <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
              </div>
              <div class="button-glow"></div>
            </button>

            <button class="cta-button secondary" @click="viewPricing">
              <span>查看定价方案</span>
              <div class="button-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </button>
          </div>

          <!-- 社会证明 -->
          <div class="hero-stats">
            <div class="stat-item">
              <div class="stat-number">50K+</div>
              <div class="stat-label">活跃用户</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">1M+</div>
              <div class="stat-label">创作作品</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">99.9%</div>
              <div class="stat-label">用户满意度</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">24/7</div>
              <div class="stat-label">在线服务</div>
            </div>
          </div>
        </div>

        <!-- 3D视觉效果 -->
        <div class="hero-visual">
          <div class="ai-showcase">
            <!-- 中央AI大脑 -->
            <div class="ai-brain">
              <div class="brain-core">
                <div class="core-inner"></div>
                <div class="core-pulse"></div>
              </div>
            </div>

            <!-- 神经网络节点 -->
            <div class="neural-network">
              <div class="node node-1"></div>
              <div class="node node-2"></div>
              <div class="node node-3"></div>
              <div class="node node-4"></div>
              <div class="connection connection-1"></div>
              <div class="connection connection-2"></div>
              <div class="connection connection-3"></div>
            </div>

            <!-- 功能预览卡片 -->
            <div class="feature-preview-cards">
              <div class="preview-card chat-preview">
                <div class="card-header">AI对话</div>
                <div class="card-content">
                  <div class="chat-bubble">你好！我是AI助手</div>
                  <div class="typing-indicator">
                    <span></span><span></span><span></span>
                  </div>
                </div>
              </div>

              <div class="preview-card art-preview">
                <div class="card-header">AI绘画</div>
                <div class="card-content">
                  <div class="art-canvas">
                    <div class="brush-stroke"></div>
                    <div class="color-palette">
                      <div class="color" style="background: #ff6b6b"></div>
                      <div class="color" style="background: #4ecdc4"></div>
                      <div class="color" style="background: #45b7d1"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特色 -->
    <section class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">核心功能</h2>
          <p class="section-subtitle">探索AI创作的无限可能</p>
        </div>

        <div class="features-grid">
          <div class="feature-card" @click="navigateToFeature('/chat')">
            <div class="feature-icon">
              <div class="icon-wrapper chat-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
            <h3 class="feature-title">智能对话</h3>
            <p class="feature-description">与AI进行自然对话，获得创意灵感和专业建议</p>
            <div class="feature-arrow">→</div>
          </div>

          <div class="feature-card" @click="navigateToFeature('/drawing')">
            <div class="feature-icon">
              <div class="icon-wrapper art-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
            <h3 class="feature-title">AI绘画</h3>
            <p class="feature-description">文字转图像，创造独特的艺术作品</p>
            <div class="feature-arrow">→</div>
          </div>

          <div class="feature-card" @click="navigateToFeature('/gallery')">
            <div class="feature-icon">
              <div class="icon-wrapper gallery-icon">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                  <path d="M21 15l-5-5L5 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
            <h3 class="feature-title">作品展示</h3>
            <p class="feature-description">分享您的创作，发现更多精彩作品</p>
            <div class="feature-arrow">→</div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content">
          <h2 class="cta-title">准备开始创作了吗？</h2>
          <p class="cta-description">加入我们，体验AI创作的无限可能</p>
          <div class="cta-actions">
            <button class="cta-button primary large" @click="startCreating">
              免费开始创作
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores'
import { onMounted, ref } from 'vue'

const router = useRouter()
const userStore = useUserStore()

// 预生成粒子样式，避免每次渲染重新计算
const particleStyles = ref([])

// 初始化粒子样式
const initParticleStyles = () => {
  particleStyles.value = Array.from({ length: 20 }, (_, index) => {
    const delay = Math.random() * 5
    const duration = 3 + Math.random() * 4
    const size = 2 + Math.random() * 4
    const x = Math.random() * 100
    const y = Math.random() * 100

    return {
      left: `${x}%`,
      top: `${y}%`,
      width: `${size}px`,
      height: `${size}px`,
      animationDelay: `${delay}s`,
      animationDuration: `${duration}s`
    }
  })
}

// 获取粒子样式
const getParticleStyle = (index) => {
  return particleStyles.value[index] || {}
}

// 页面交互函数
const startCreating = (event) => {
  // 添加点击动画效果
  const button = event.target.closest('.cta-button')
  if (button) {
    button.style.transform = 'scale(0.95)'
    setTimeout(() => {
      button.style.transform = ''
    }, 150)
  }

  if (userStore.isLoggedIn) {
    router.push('/chat')
  } else {
    router.push('/login?redirect=/chat')
  }
}

const exploreGallery = () => {
  router.push('/gallery')
}

const viewPricing = () => {
  router.push('/pricing')
}

const navigateToFeature = (path) => {
  if (path === '/chat' || path === '/drawing') {
    if (!userStore.isLoggedIn) {
      router.push(`/login?redirect=${path}`)
      return
    }
  }
  router.push(path)
}

// 页面加载动画
onMounted(() => {
  // 初始化粒子样式
  initParticleStyles()

  // 添加页面加载动画
  const heroElements = document.querySelectorAll('.hero-content > *')
  heroElements.forEach((el, index) => {
    el.style.opacity = '0'
    el.style.transform = 'translateY(30px)'
    setTimeout(() => {
      el.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
      el.style.opacity = '1'
      el.style.transform = 'translateY(0)'
    }, index * 200)
  })
})
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

// CSS 自定义属性定义
:root {
  --apple-blue: #007AFF;
  --apple-white: #FFFFFF;
  --apple-dark: #1D1D1F;
  --apple-gray: #86868B;
  --apple-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.home-page {
  min-height: 100vh;
  background: $bg-color;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  color: $text-color-primary;
  overflow-x: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 $spacing-lg;

  @media (max-width: 768px) {
    padding: 0 $spacing-md;
  }

  @media (max-width: 480px) {
    padding: 0 $spacing-sm;
  }
}

.cta-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 28px;
  border-radius: 50px;
  font-size: 17px;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: var(--apple-transition);
  min-width: 140px;
  position: relative;
  overflow: hidden;

  &.primary {
    background: linear-gradient(135deg, var(--apple-blue) 0%, #5856D6 100%);
    color: var(--apple-white);
    box-shadow: 0 4px 15px rgba(0, 122, 255, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
    }

    &:active {
      transform: translateY(0);
    }

    svg {
      transition: transform 0.2s ease;
    }

    &:hover svg {
      transform: translateX(2px);
    }
  }

  &.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--apple-blue);
    border: 1.5px solid rgba(0, 122, 255, 0.3);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    &:hover {
      background: rgba(0, 122, 255, 0.1);
      border-color: var(--apple-blue);
      transform: translateY(-1px);
    }
  }

  &.large {
    padding: 18px 36px;
    font-size: 18px;
    min-width: 200px;
  }
}

// 震撼英雄区域
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  overflow: hidden;

  // 动态背景
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;

    .gradient-orb {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.7;
      animation: float 6s ease-in-out infinite;

      &.orb-1 {
        width: 300px;
        height: 300px;
        background: $gradient-primary;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.orb-2 {
        width: 200px;
        height: 200px;
        background: $gradient-accent;
        top: 60%;
        right: 20%;
        animation-delay: 2s;
      }

      &.orb-3 {
        width: 150px;
        height: 150px;
        background: $gradient-secondary;
        bottom: 20%;
        left: 60%;
        animation-delay: 4s;
      }
    }

    .floating-particles {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .particle {
        position: absolute;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
        animation: particle-float 4s linear infinite;
      }
    }
  }

  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: $spacing-xl;
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      text-align: center;
      gap: $spacing-lg;
    }
  }

  .hero-content {
    color: white;

    .hero-badge {
      position: relative;
      display: inline-block;
      background: rgba(99, 102, 241, 0.1);
      border: 1px solid rgba(99, 102, 241, 0.3);
      color: #a78bfa;
      padding: 12px 24px;
      border-radius: 50px;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 32px;
      backdrop-filter: blur(10px);

      .badge-glow {
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: $gradient-primary;
        border-radius: 50px;
        z-index: -1;
        opacity: 0.3;
        filter: blur(8px);
      }
    }

    .hero-title {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 800;
      line-height: 1.1;
      margin-bottom: 24px;

      @media (max-width: 768px) {
        font-size: clamp(2rem, 8vw, 3rem);
        line-height: 1.2;
        margin-bottom: 20px;
      }

      @media (max-width: 480px) {
        font-size: clamp(1.8rem, 10vw, 2.5rem);
        margin-bottom: 16px;
      }

      .title-line {
        display: block;
        margin-bottom: 8px;
      }

      .gradient-text {
        background: $gradient-primary;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .highlight-text {
        background: $gradient-accent;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .hero-subtitle {
      font-size: 1.25rem;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 32px;
      line-height: 1.6;
      max-width: 600px;

      @media (max-width: 768px) {
        font-size: 1.1rem;
        margin-bottom: 24px;
        max-width: 100%;
      }

      @media (max-width: 480px) {
        font-size: 1rem;
        margin-bottom: 20px;
        line-height: 1.5;
      }
    }

    .value-propositions {
      display: flex;
      gap: $spacing-lg;
      margin-bottom: 40px;
      justify-content: flex-start;

      @media (max-width: 1024px) {
        justify-content: center;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: $spacing-md;
      }

      .value-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);

        .value-icon {
          font-size: 20px;
        }
      }
    }

    .hero-actions {
      display: flex;
      gap: $spacing-md;
      margin-bottom: 48px;
      justify-content: flex-start;

      @media (max-width: 1024px) {
        justify-content: center;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
        gap: $spacing-sm;
      }

      .cta-button {
        position: relative;
        padding: 16px 32px;
        border-radius: 50px;
        font-size: 16px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;

        @media (max-width: 768px) {
          width: 100%;
          max-width: 280px;
          padding: 14px 28px;
          font-size: 15px;
        }

        @media (max-width: 480px) {
          max-width: 100%;
          padding: 12px 24px;
          font-size: 14px;
        }

        &.primary {
          background: $gradient-primary;
          color: white;
          box-shadow: $box-shadow-lg;

          .button-content {
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            z-index: 2;
          }

          .button-glow {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: $gradient-primary;
            border-radius: 50px;
            z-index: 1;
            opacity: 0;
            filter: blur(20px);
            transition: opacity 0.3s ease;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: $box-shadow-xl;

            .button-glow {
              opacity: 0.7;
            }
          }
        }

        &.secondary {
          background: rgba(255, 255, 255, 0.1);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.2);
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
          }
        }
      }
    }

    .hero-stats {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: $spacing-lg;
      margin-top: 48px;

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-md;
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
        gap: $spacing-sm;
      }

      .stat-item {
        text-align: center;
        padding: $spacing-md;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 16px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 800;
          background: $gradient-accent;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }
      }
    }
  }

  .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .ai-showcase {
      position: relative;
      width: 100%;
      height: 500px;
      display: flex;
      justify-content: center;
      align-items: center;

      .ai-brain {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 280px;
        height: 280px;

        .brain-core {
          width: 100%;
          height: 100%;
          position: relative;
          background: $gradient-primary;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          box-shadow: $box-shadow-xl;
          animation: float 6s ease-in-out infinite;
          backdrop-filter: blur(10px);
          border: 2px solid rgba(255, 255, 255, 0.3);

          .core-inner {
            width: 60%;
            height: 60%;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              top: 20%;
              left: 20%;
              width: 60%;
              height: 60%;
              background: rgba(255, 255, 255, 0.3);
              border-radius: 50%;
            }
          }

          .core-pulse {
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: pulse 2s ease-in-out infinite;
          }
        }
      }

      .neural-network {
        position: absolute;
        width: 500px;
        height: 500px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        .node {
          position: absolute;
          width: 12px;
          height: 12px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 50%;
          animation: pulse 2s infinite;

          &.node-1 { top: 15%; left: 25%; animation-delay: 0s; }
          &.node-2 { top: 25%; right: 15%; animation-delay: 0.5s; }
          &.node-3 { bottom: 25%; left: 15%; animation-delay: 1s; }
          &.node-4 { bottom: 15%; right: 25%; animation-delay: 1.5s; }
        }

        .connection {
          position: absolute;
          height: 2px;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
          animation: flow 3s infinite;

          &.connection-1 {
            top: 20%;
            left: 30%;
            width: 40%;
            transform: rotate(15deg);
          }

          &.connection-2 {
            top: 45%;
            left: 15%;
            width: 70%;
            transform: rotate(-8deg);
          }

          &.connection-3 {
            bottom: 20%;
            left: 20%;
            width: 60%;
            transform: rotate(20deg);
          }
        }
      }
    }

      // 功能预览卡片
      .feature-preview-cards {
        position: absolute;
        top: 10%;
        right: 5%;
        display: flex;
        flex-direction: column;
        gap: 16px;
        z-index: 10;

      .preview-card {
        width: 200px;
        background: rgba(255, 255, 255, 0.06);
        backdrop-filter: blur(12px);
        border: 1px solid rgba(255, 255, 255, 0.12);
        border-radius: 10px;
        padding: 14px;
        box-shadow: 0 3px 15px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        animation: float 6s ease-in-out infinite;

        &:hover {
          transform: translateY(-3px);
          background: rgba(255, 255, 255, 0.12);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        &.chat-preview {
          animation-delay: 0.5s;
        }

        &.art-preview {
          animation-delay: 1s;
        }

        .card-header {
          font-size: 14px;
          font-weight: 600;
          color: white;
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          gap: 6px;

          &::before {
            content: '';
            width: 6px;
            height: 6px;
            background: $primary-color;
            border-radius: 50%;
            box-shadow: 0 0 8px $primary-color;
          }
        }

        .card-content {
          .chat-bubble {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 12px;
            margin-bottom: 8px;
            position: relative;

            &::after {
              content: '';
              position: absolute;
              bottom: -5px;
              left: 20px;
              width: 0;
              height: 0;
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-top: 5px solid rgba(255, 255, 255, 0.15);
            }
          }

          .typing-indicator {
            display: flex;
            gap: 4px;
            padding-left: 16px;

            span {
              width: 6px;
              height: 6px;
              background: rgba(255, 255, 255, 0.6);
              border-radius: 50%;
              animation: typing 1.4s infinite;

              &:nth-child(2) { animation-delay: 0.2s; }
              &:nth-child(3) { animation-delay: 0.4s; }
            }
          }

          .art-canvas {
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            position: relative;
            overflow: hidden;

            .brush-stroke {
              position: absolute;
              top: 20px;
              left: 20px;
              width: 60px;
              height: 3px;
              background: linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1);
              border-radius: 2px;
              animation: draw 3s infinite;
            }

            .color-palette {
              position: absolute;
              bottom: 10px;
              right: 10px;
              display: flex;
              gap: 6px;

              .color {
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 2px solid rgba(255, 255, 255, 0.3);
              }
            }
          }
        }
      }
    }
  }
}

// 功能特色区域
.features-section {
  padding: 80px 0;
  background: var(--apple-white);

  .section-header {
    text-align: center;
    margin-bottom: 64px;

    .section-title {
      font-size: 48px;
      font-weight: 700;
      color: var(--apple-dark);
      margin-bottom: 16px;

      @media (max-width: 768px) {
        font-size: 36px;
      }
    }

    .section-subtitle {
      font-size: 20px;
      color: var(--apple-gray);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    max-width: 1000px;
    margin: 0 auto;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 24px;
    }
  }

  .feature-card {
    background: var(--apple-white);
    border-radius: 20px;
    padding: 40px 32px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: var(--apple-transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(0, 122, 255, 0.05) 0%, transparent 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

      &::before {
        opacity: 1;
      }

      .feature-arrow {
        transform: translateX(4px);
      }
    }

    .feature-icon {
      margin-bottom: 24px;

      .icon-wrapper {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        color: white;

        &.chat-icon {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.art-icon {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.gallery-icon {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
      }
    }

    .feature-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--apple-dark);
      margin-bottom: 12px;
    }

    .feature-description {
      font-size: 16px;
      color: var(--apple-gray);
      line-height: 1.5;
      margin-bottom: 24px;
    }

    .feature-arrow {
      font-size: 20px;
      color: var(--apple-blue);
      font-weight: 600;
      transition: transform 0.2s ease;
    }
  }
}

// CTA区域
.cta-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    opacity: 0.3;
  }

  .cta-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    margin: 0 auto;

    .cta-title {
      font-size: 48px;
      font-weight: 700;
      color: white;
      margin-bottom: 16px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 36px;
      }
    }

    .cta-description {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 40px;
      line-height: 1.5;

      @media (max-width: 768px) {
        font-size: 18px;
      }
    }

    .cta-actions {
      .cta-button {
        &.primary {
          background: white;
          color: var(--apple-blue);
          box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);

          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }
}

// 现代化动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes draw {
  0% {
    width: 0;
    opacity: 0;
  }
  50% {
    width: 60px;
    opacity: 1;
  }
  100% {
    width: 60px;
    opacity: 0.7;
  }
}

@keyframes flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

// 移动端专用优化
@media (max-width: 768px) {
  .home-page {
    // 确保移动端不会出现横向滚动
    overflow-x: hidden;

    // 优化触摸滚动
    -webkit-overflow-scrolling: touch;
  }

  // 移动端文本选择优化
  .hero-title,
  .hero-subtitle {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  // 移动端按钮触摸优化
  .cta-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .hero-section {
    min-height: 90vh; // 减少高度以适应小屏幕
    padding: $spacing-lg 0;
  }

  .features-section,
  .cta-section {
    padding: $spacing-xl 0; // 减少垂直间距
  }
}

</style>
