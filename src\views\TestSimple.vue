<template>
  <div class="test-simple">
    <div class="container">
      <h1>简单测试页面</h1>
      <p>如果您能看到这个页面，说明基础功能正常。</p>
      
      <div class="test-section">
        <h2>用户Store测试</h2>
        <div class="test-item">
          <span>登录状态: </span>
          <span :class="userStore.isLoggedIn ? 'success' : 'warning'">
            {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
          </span>
        </div>
        <div class="test-item">
          <span>用户名: </span>
          <span>{{ userStore.userName }}</span>
        </div>
        <div class="test-item">
          <span>用户角色: </span>
          <span>{{ userStore.userRole }}</span>
        </div>
      </div>

      <div class="test-section">
        <h2>本地存储测试</h2>
        <div class="test-item">
          <span>用户数据库: </span>
          <span>{{ localUsers.length }} 个用户</span>
        </div>
        <div class="test-item">
          <span>记住我信息: </span>
          <span>{{ rememberMe || '无' }}</span>
        </div>
      </div>

      <div class="test-section">
        <h2>功能测试</h2>
        <div class="button-group">
          <button @click="testRegister" class="test-btn">测试注册</button>
          <button @click="testLogin" class="test-btn">测试登录</button>
          <button @click="testLogout" class="test-btn">测试登出</button>
          <button @click="refreshData" class="test-btn">刷新数据</button>
        </div>
      </div>

      <div class="test-section">
        <h2>导航测试</h2>
        <div class="button-group">
          <router-link to="/register" class="nav-btn">注册页面</router-link>
          <router-link to="/login" class="nav-btn">登录页面</router-link>
          <router-link to="/home" class="nav-btn">首页</router-link>
          <router-link to="/admin/users" class="nav-btn">用户管理</router-link>
        </div>
      </div>

      <div class="test-section">
        <h2>数据展示</h2>
        <details>
          <summary>本地用户数据</summary>
          <pre>{{ JSON.stringify(localUsers, null, 2) }}</pre>
        </details>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 响应式数据
const localUsers = ref([])
const rememberMe = ref('')

// 刷新数据
const refreshData = () => {
  try {
    localUsers.value = userStore.getLocalUserDatabase()
    rememberMe.value = userStore.getRememberMeInfo()
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败: ' + error.message)
  }
}

// 测试注册
const testRegister = async () => {
  try {
    const testUser = {
      username: 'testuser_' + Date.now(),
      email: 'test_' + Date.now() + '@example.com',
      password: '123456'
    }
    
    const result = await userStore.register(testUser)
    if (result.success) {
      ElMessage.success('测试注册成功')
      refreshData()
    }
  } catch (error) {
    ElMessage.error('测试注册失败: ' + error.message)
  }
}

// 测试登录
const testLogin = async () => {
  try {
    const result = await userStore.login({
      username: 'admin',
      password: '123456',
      rememberMe: true
    })
    
    if (result.success) {
      ElMessage.success('测试登录成功')
      refreshData()
    }
  } catch (error) {
    ElMessage.error('测试登录失败: ' + error.message)
  }
}

// 测试登出
const testLogout = async () => {
  try {
    await userStore.logout()
    ElMessage.success('测试登出成功')
    refreshData()
  } catch (error) {
    ElMessage.error('测试登出失败: ' + error.message)
  }
}

// 组件挂载时刷新数据
onMounted(() => {
  refreshData()
})
</script>

<style lang="scss" scoped>
.test-simple {
  min-height: 100vh;
  background: #f8fafc;
  padding: 2rem;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

h1 {
  color: #1f2937;
  margin-bottom: 1rem;
  text-align: center;
}

h2 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.test-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.test-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  
  .success {
    color: #10b981;
    font-weight: 600;
  }
  
  .warning {
    color: #f59e0b;
    font-weight: 600;
  }
}

.button-group {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.test-btn {
  padding: 0.75rem 1.5rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
  
  &:hover {
    background: #2563eb;
  }
}

.nav-btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: #10b981;
  color: white;
  text-decoration: none;
  border-radius: 6px;
  font-weight: 500;
  transition: background 0.2s;
  
  &:hover {
    background: #059669;
  }
}

details {
  margin-top: 1rem;
  
  summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }
  
  pre {
    background: #f3f4f6;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.875rem;
  }
}
</style>
