<template>
  <div v-if="isAdmin" class="admin-navigation">
    <!-- 管理员快捷菜单 -->
    <el-dropdown trigger="click" @command="handleCommand">
      <el-button type="primary" :icon="Setting">
        管理中心
        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="api-key-management" :icon="Key">
            <span>API密钥管理</span>
          </el-dropdown-item>
          <el-dropdown-item command="users" :icon="User">
            <span>用户管理</span>
          </el-dropdown-item>
          <el-dropdown-item command="system-monitor" :icon="Monitor" divided>
            <span>系统监控</span>
          </el-dropdown-item>
          <el-dropdown-item command="api-key-rotation-demo" :icon="Refresh">
            <span>密钥轮询演示</span>
          </el-dropdown-item>
          <el-dropdown-item command="api-key-basic-test" :icon="Operation">
            <span>API测试</span>
          </el-dropdown-item>
          <el-dropdown-item command="api-key-batch-tester" :icon="Operation">
            <span>批量检测</span>
          </el-dropdown-item>
          <el-dropdown-item command="system-settings" :icon="Tools" divided>
            <span>系统设置</span>
          </el-dropdown-item>
          <el-dropdown-item command="logs" :icon="Document">
            <span>系统日志</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 管理员状态指示器 -->
    <div class="admin-status-indicator">
      <el-badge :value="pendingTasksCount" :max="99" :hidden="pendingTasksCount === 0">
        <el-button :icon="Bell" circle size="small" @click="showNotifications" />
      </el-badge>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import {
  Setting, ArrowDown, Key, User, Monitor, Refresh,
  Tools, Document, Bell, Operation
} from '@element-plus/icons-vue'

const router = useRouter()
const userStore = useUserStore()

// 计算属性 - 安全地访问userStore
const isAdmin = computed(() => {
  try {
    // 确保userStore存在且已初始化
    if (!userStore) {
      return false
    }
    return userStore.isAdmin ?? false
  } catch (error) {
    console.warn('获取管理员状态时出错:', error)
    return false
  }
})
const pendingTasksCount = ref(0) // 这里可以从API获取待处理任务数量

// 方法
const handleCommand = (command) => {
  switch (command) {
    case 'api-key-management':
      router.push({ name: 'ApiKeyManagement' })
      break
    case 'users':
      router.push({ name: 'UsersAdmin' })
      break
    case 'system-monitor':
      router.push({ name: 'SystemMonitor' })
      break
    case 'api-key-rotation-demo':
      router.push({ name: 'ApiKeyRotationDemo' })
      break
    case 'api-key-basic-test':
      router.push({ name: 'ApiKeyBasicTest' })
      break
    case 'api-key-batch-tester':
      router.push({ name: 'ApiKeyBatchTester' })
      break
    case 'system-settings':
      router.push({ name: 'SystemSettings' })
      break
    case 'logs':
      router.push({ name: 'SystemLogs' })
      break
    default:
      ElMessage.warning('未知命令')
  }
}

const showNotifications = () => {
  ElMessage.info('暂无新通知')
}
</script>

<style scoped>
.admin-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-status-indicator {
  display: flex;
  align-items: center;
}
</style>
