<template>
  <div v-if="isAdmin" class="admin-navigation">
    <!-- 管理员快捷菜单 -->
    <div class="admin-dropdown" @click.stop>
      <button class="admin-btn" @click="toggleAdminMenu">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M12 15l-3-3h6l-3 3z" fill="currentColor"/>
          <path d="M9 6h6v2H9zM9 10h6v2H9zM9 14h6v2H9z" fill="currentColor"/>
        </svg>
        <span>管理中心</span>
        <svg class="dropdown-arrow" :class="{ active: adminMenuOpen }" width="12" height="12" viewBox="0 0 24 24" fill="none">
          <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <!-- 下拉菜单 -->
      <div class="admin-dropdown-menu" :class="{ active: adminMenuOpen }">
        <button class="admin-menu-item" @click="handleCommand('api-key-management')">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5M7 7l3.5 3.5M8.5 5.5L10 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          API密钥管理
        </button>
        <button class="admin-menu-item" @click="handleCommand('users')">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
          </svg>
          用户管理
        </button>
        <button class="admin-menu-item" @click="handleCommand('system-monitor')">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <rect x="2" y="3" width="20" height="14" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
            <line x1="8" y1="21" x2="16" y2="21" stroke="currentColor" stroke-width="2"/>
            <line x1="12" y1="17" x2="12" y2="21" stroke="currentColor" stroke-width="2"/>
          </svg>
          系统监控
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 计算属性 - 安全地访问userStore
const isAdmin = computed(() => {
  try {
    // 确保userStore存在且已初始化
    if (!userStore) {
      return false
    }
    return userStore.isAdmin ?? false
  } catch (error) {
    console.warn('获取管理员状态时出错:', error)
    return false
  }
})

const adminMenuOpen = ref(false)

// 方法
const toggleAdminMenu = () => {
  adminMenuOpen.value = !adminMenuOpen.value
}

const handleCommand = (command) => {
  adminMenuOpen.value = false // 关闭菜单

  switch (command) {
    case 'api-key-management':
      router.push({ name: 'ApiKeyManagement' })
      break
    case 'users':
      router.push({ name: 'UsersAdmin' })
      break
    case 'system-monitor':
      router.push({ name: 'SystemMonitor' })
      break
    default:
      console.warn('未知命令:', command)
  }
}
</script>

<style scoped>
.admin-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-dropdown {
  position: relative;
}

.admin-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 20px;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(102, 126, 234, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
  }

  .dropdown-arrow {
    transition: transform 0.2s ease;

    &.active {
      transform: rotate(180deg);
    }
  }
}

.admin-dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  min-width: 200px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;

  &.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }
}

.admin-menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:first-child {
    border-radius: 12px 12px 0 0;
  }

  &:last-child {
    border-radius: 0 0 12px 12px;
  }

  &:hover {
    background: rgba(102, 126, 234, 0.05);
    color: #667eea;
  }

  svg {
    flex-shrink: 0;
  }
}
</style>
