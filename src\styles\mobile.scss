// 移动端优化样式 - 使用现代@use语法
@use './variables.scss' as *;

// 触摸优化
.touch-optimized {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

// 移动端按钮优化
.mobile-btn {
  min-height: 44px;
  min-width: 44px;
  padding: $spacing-md;
  font-size: $font-size-base;
  
  &.large {
    min-height: 56px;
    padding: $spacing-lg;
    font-size: $font-size-lg;
  }
}

// 移动端输入框优化
.mobile-input {
  font-size: 16px; // 防止iOS缩放
  
  .el-input__inner {
    font-size: 16px;
    padding: $spacing-md;
    border-radius: $border-radius-base;
  }
}

// 移动端卡片优化
.mobile-card {
  border-radius: $border-radius-base;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: $spacing-md;
  
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

// 移动端导航优化
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: $bg-color;
  border-top: 1px solid $border-color-light;
  padding: $spacing-sm 0;
  z-index: 1000;
  
  .nav-items {
    display: flex;
    justify-content: space-around;
    align-items: center;
    
    .nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: $spacing-xs;
      padding: $spacing-sm;
      color: $text-color-secondary;
      text-decoration: none;
      transition: color 0.2s ease;
      
      &.active {
        color: $primary-color;
      }
      
      .nav-icon {
        font-size: 1.5rem;
      }
      
      .nav-label {
        font-size: $font-size-xs;
      }
    }
  }
}

// 移动端模态框优化
.mobile-modal {
  .el-dialog {
    width: 95vw !important;
    margin: 5vh auto !important;
    
    .el-dialog__body {
      padding: $spacing-lg;
      max-height: 70vh;
      overflow-y: auto;
    }
  }
}

// 移动端抽屉优化
.mobile-drawer {
  .el-drawer {
    &.el-drawer--rtl {
      width: 85vw !important;
    }
    
    &.el-drawer--ltr {
      width: 85vw !important;
    }
    
    &.el-drawer--ttb,
    &.el-drawer--btt {
      height: 70vh !important;
    }
  }
}

// 移动端表格优化
.mobile-table {
  .el-table {
    font-size: $font-size-sm;
    
    .el-table__header th {
      padding: $spacing-sm;
      font-size: $font-size-xs;
    }
    
    .el-table__body td {
      padding: $spacing-sm;
    }
  }
}

// 移动端分页优化
.mobile-pagination {
  .el-pagination {
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }
    
    .el-pager {
      .number {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}

// 移动端表单优化
.mobile-form {
  .el-form-item {
    margin-bottom: $spacing-lg;
    
    .el-form-item__label {
      font-size: $font-size-base;
      font-weight: 500;
      margin-bottom: $spacing-sm;
    }
    
    .el-form-item__content {
      .el-input,
      .el-select,
      .el-textarea {
        .el-input__inner,
        .el-textarea__inner {
          font-size: 16px; // 防止iOS缩放
          padding: $spacing-md;
        }
      }
    }
  }
}

// 移动端网格优化
.mobile-grid {
  display: grid;
  gap: $spacing-md;
  
  &.grid-1 {
    grid-template-columns: 1fr;
  }
  
  &.grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  &.grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}

// 移动端滚动优化
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  
  &::-webkit-scrollbar {
    display: none;
  }
}

// 移动端安全区域适配
.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-right: env(safe-area-inset-right);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

// 移动端手势优化
.swipe-item {
  touch-action: pan-y;
  
  &.swipe-horizontal {
    touch-action: pan-x;
  }
  
  &.swipe-none {
    touch-action: none;
  }
}

// 移动端加载状态优化
.mobile-loading {
  .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
    
    .el-loading-spinner {
      .el-loading-text {
        font-size: $font-size-base;
        margin-top: $spacing-md;
      }
    }
  }
}

// 移动端消息提示优化
.mobile-message {
  .el-message {
    min-width: 280px;
    max-width: 90vw;
    left: 50% !important;
    transform: translateX(-50%);
    
    .el-message__content {
      font-size: $font-size-base;
      line-height: 1.5;
    }
  }
}

// 移动端图片优化
.mobile-image {
  img {
    max-width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: $border-radius-base;
  }
  
  &.aspect-square {
    aspect-ratio: 1;
    overflow: hidden;
  }
  
  &.aspect-video {
    aspect-ratio: 16/9;
    overflow: hidden;
  }
}

// 移动端文本优化
.mobile-text {
  line-height: 1.6;
  word-break: break-word;
  
  &.large {
    font-size: $font-size-lg;
    line-height: 1.5;
  }
  
  &.small {
    font-size: $font-size-sm;
    line-height: 1.4;
  }
}

// 移动端间距优化
.mobile-spacing {
  &.compact {
    .section {
      margin-bottom: $spacing-lg;
    }
    
    .item {
      margin-bottom: $spacing-md;
    }
  }
  
  &.comfortable {
    .section {
      margin-bottom: $spacing-xl;
    }
    
    .item {
      margin-bottom: $spacing-lg;
    }
  }
}

// 移动端动画优化
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 移动端特定断点样式
@media (max-width: 480px) {
  .container {
    padding: 0 $spacing-md;
  }

  .section-padding {
    padding: $spacing-lg $spacing-md;
  }

  .card-padding {
    padding: $spacing-md;
  }
}

// 聊天界面移动端优化
.chat-mobile-optimized {
  @media (max-width: 768px) {
    // 消息气泡优化
    .message-bubble {
      max-width: 90%;
      padding: 0.875rem 1rem;
      border-radius: 16px;
      font-size: 0.9rem;
    }

    // 输入框优化
    .chat-input {
      padding: 0.75rem;
      border-radius: 20px;

      .input-field {
        font-size: 16px; // 防止iOS缩放
      }
    }

    // 按钮优化
    .action-btn {
      min-height: 44px; // iOS推荐的最小触摸目标
      min-width: 44px;
      padding: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .message-bubble {
      max-width: 95%;
      padding: 0.75rem 0.875rem;
      border-radius: 14px;
      font-size: 0.875rem;
    }

    .chat-input {
      padding: 0.5rem;
      border-radius: 16px;
    }

    .action-btn {
      min-height: 40px;
      min-width: 40px;
      padding: 0.625rem;
    }
  }
}

// 聊天界面移动端优化
.chat-mobile-optimized {
  @media (max-width: 768px) {
    // 消息气泡优化
    .message-bubble {
      max-width: 90%;
      padding: 0.875rem 1rem;
      border-radius: 16px;
      font-size: 0.9rem;
    }

    // 输入框优化
    .chat-input {
      padding: 0.75rem;
      border-radius: 20px;

      .input-field {
        font-size: 16px; // 防止iOS缩放
      }
    }

    // 按钮优化
    .action-btn {
      min-height: 44px; // iOS推荐的最小触摸目标
      min-width: 44px;
      padding: 0.75rem;
    }

    // 侧边栏优化
    .sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      z-index: 1000;
      transform: translateX(-100%);
      transition: transform 0.3s ease-in-out;

      &.open {
        transform: translateX(0);
      }
    }

    // 遮罩层
    .sidebar-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease-in-out;

      &.active {
        opacity: 1;
        visibility: visible;
      }
    }
  }

  @media (max-width: 480px) {
    .message-bubble {
      max-width: 95%;
      padding: 0.75rem 0.875rem;
      border-radius: 14px;
      font-size: 0.875rem;
    }

    .chat-input {
      padding: 0.5rem;
      border-radius: 16px;
    }

    .action-btn {
      min-height: 40px;
      min-width: 40px;
      padding: 0.625rem;
    }
  }
}

// 横屏优化
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-optimize {
    .header {
      height: 48px;
    }
    
    .content {
      padding: $spacing-sm 0;
    }
  }
}
