import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { APP_CONFIG } from '@/config'

export const useAppStore = defineStore('app', () => {
  // 状态
  const sidebarCollapsed = ref(false)
  const theme = ref('light')
  const language = ref('zh-CN')
  const loading = ref(false)
  const device = ref('desktop')
  const screenSize = ref({
    width: window.innerWidth,
    height: window.innerHeight,
  })

  // 计算属性 - 添加安全检查
  const isMobile = computed(() => {
    try {
      return device.value === 'mobile' || screenSize.value.width < 768
    } catch (error) {
      console.warn('获取移动端状态时出错:', error)
      return window.innerWidth < 768
    }
  })

  const isTablet = computed(() => {
    try {
      return device.value === 'tablet' || (screenSize.value.width >= 768 && screenSize.value.width < 1024)
    } catch (error) {
      console.warn('获取平板状态时出错:', error)
      return window.innerWidth >= 768 && window.innerWidth < 1024
    }
  })

  const isDesktop = computed(() => {
    try {
      return device.value === 'desktop' || screenSize.value.width >= 1024
    } catch (error) {
      console.warn('获取桌面端状态时出错:', error)
      return window.innerWidth >= 1024
    }
  })

  const sidebarWidth = computed(() => {
    try {
      if (isMobile.value) return 0
      return sidebarCollapsed.value ? 64 : 240
    } catch (error) {
      console.warn('获取侧边栏宽度时出错:', error)
      return 240
    }
  })

  const mainContentStyle = computed(() => {
    try {
      return {
        marginLeft: isMobile.value ? '0' : `${sidebarWidth.value}px`,
        transition: 'margin-left 0.3s ease',
      }
    } catch (error) {
      console.warn('获取主内容样式时出错:', error)
      return {
        marginLeft: '0',
        transition: 'margin-left 0.3s ease',
      }
    }
  })

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    saveAppSettings()
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    saveAppSettings()
  }

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
    applyTheme()
    saveAppSettings()
  }

  // 设置主题
  const setTheme = (newTheme) => {
    theme.value = newTheme
    applyTheme()
    saveAppSettings()
  }

  // 应用主题
  const applyTheme = () => {
    document.documentElement.setAttribute('data-theme', theme.value)
    
    if (theme.value === 'dark') {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  // 设置语言
  const setLanguage = (lang) => {
    language.value = lang
    saveAppSettings()
  }

  // 设置设备类型
  const setDevice = (deviceType) => {
    device.value = deviceType
  }

  // 更新屏幕尺寸
  const updateScreenSize = () => {
    screenSize.value = {
      width: window.innerWidth,
      height: window.innerHeight,
    }

    // 根据屏幕尺寸自动设置设备类型
    if (screenSize.value.width < 768) {
      device.value = 'mobile'
      // 移动端自动收起侧边栏
      sidebarCollapsed.value = true
    } else if (screenSize.value.width < 1024) {
      device.value = 'tablet'
    } else {
      device.value = 'desktop'
    }
  }

  // 设置全局加载状态
  const setLoading = (isLoading) => {
    loading.value = isLoading
  }

  // 显示全局加载
  const showLoading = () => {
    loading.value = true
  }

  // 隐藏全局加载
  const hideLoading = () => {
    loading.value = false
  }

  // 保存应用设置
  const saveAppSettings = () => {
    const settings = {
      sidebarCollapsed: sidebarCollapsed.value,
      theme: theme.value,
      language: language.value,
      device: device.value,
    }
    localStorage.setItem('app_settings', JSON.stringify(settings))
  }

  // 加载应用设置
  const loadAppSettings = () => {
    try {
      const savedSettings = localStorage.getItem('app_settings')
      if (savedSettings) {
        const settings = JSON.parse(savedSettings)
        sidebarCollapsed.value = settings.sidebarCollapsed ?? false
        theme.value = settings.theme ?? 'light'
        language.value = settings.language ?? 'zh-CN'
        device.value = settings.device ?? 'desktop'
        
        // 应用主题
        applyTheme()
      }
    } catch (error) {
      console.error('加载应用设置失败:', error)
    }
  }

  // 初始化应用
  const initApp = async () => {
    try {
      console.log('📱 开始初始化应用设置...')

      // 加载设置
      loadAppSettings()
      console.log('✅ 应用设置加载完成')

      // 更新屏幕尺寸
      updateScreenSize()
      console.log('✅ 屏幕尺寸更新完成')

      // 监听窗口大小变化
      window.addEventListener('resize', updateScreenSize)
      console.log('✅ 窗口大小监听器注册完成')

      // 监听系统主题变化
      if (window.matchMedia) {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        mediaQuery.addEventListener('change', (e) => {
          if (theme.value === 'auto') {
            applyTheme()
          }
        })
        console.log('✅ 系统主题监听器注册完成')
      }

      console.log('✅ 应用初始化完成')
    } catch (error) {
      console.error('❌ 应用初始化失败:', error)
      throw error
    }
  }

  // 清理应用
  const cleanupApp = () => {
    window.removeEventListener('resize', updateScreenSize)
  }

  // 重置应用状态
  const resetAppState = () => {
    sidebarCollapsed.value = false
    theme.value = 'light'
    language.value = 'zh-CN'
    loading.value = false
    device.value = 'desktop'
    
    // 清除本地存储
    localStorage.removeItem('app_settings')
    
    // 重新应用主题
    applyTheme()
  }

  // 获取应用信息
  const getAppInfo = () => {
    return {
      name: APP_CONFIG.name,
      version: APP_CONFIG.version,
      description: APP_CONFIG.description,
      theme: theme.value,
      language: language.value,
      device: device.value,
      screenSize: screenSize.value,
    }
  }

  // 检查是否为移动端
  const checkMobile = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone']
    return mobileKeywords.some(keyword => userAgent.includes(keyword))
  }

  // 获取浏览器信息
  const getBrowserInfo = () => {
    const userAgent = navigator.userAgent
    let browser = 'Unknown'
    
    if (userAgent.includes('Chrome')) browser = 'Chrome'
    else if (userAgent.includes('Firefox')) browser = 'Firefox'
    else if (userAgent.includes('Safari')) browser = 'Safari'
    else if (userAgent.includes('Edge')) browser = 'Edge'
    else if (userAgent.includes('Opera')) browser = 'Opera'
    
    return {
      userAgent,
      browser,
      isMobile: checkMobile(),
    }
  }

  return {
    // 状态
    sidebarCollapsed,
    theme,
    language,
    loading,
    device,
    screenSize,
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    sidebarWidth,
    mainContentStyle,
    
    // 方法
    toggleSidebar,
    setSidebarCollapsed,
    toggleTheme,
    setTheme,
    applyTheme,
    setLanguage,
    setDevice,
    updateScreenSize,
    setLoading,
    showLoading,
    hideLoading,
    saveAppSettings,
    loadAppSettings,
    initApp,
    cleanupApp,
    resetAppState,
    getAppInfo,
    checkMobile,
    getBrowserInfo,
  }
})
