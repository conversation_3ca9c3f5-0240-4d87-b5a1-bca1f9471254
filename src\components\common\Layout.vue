<template>
  <div class="app-layout">
    <!-- 苹果风格顶部导航栏 -->
    <header class="app-header">
      <nav class="nav-container">
        <!-- Logo区域 -->
        <div class="nav-brand">
          <router-link to="/" class="brand-link">
            <div class="brand-icon">
              <div class="ai-logo">
                <div class="logo-circle"></div>
                <div class="logo-spark"></div>
              </div>
            </div>
            <span class="brand-text">AI创作助手</span>
          </router-link>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu" :class="{ 'nav-menu-open': mobileMenuOpen }" @click="handleMenuOverlayClick">
          <!-- 侧边菜单头部 -->
          <div class="sidebar-header">
            <div class="sidebar-brand">
              <div class="brand-icon">
                <div class="ai-logo">
                  <div class="logo-circle"></div>
                </div>
              </div>
              <span class="brand-text">AI创作助手</span>
            </div>
            <button class="sidebar-close" @click="closeMobileMenu">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>

          <router-link to="/" class="nav-item" @click="closeMobileMenu">
            <span>🏠</span>
            <span>首页</span>
          </router-link>
          <router-link to="/chat" class="nav-item" @click="closeMobileMenu">
            <span>💬</span>
            <span>AI对话</span>
          </router-link>
          <router-link to="/drawing" class="nav-item" @click="closeMobileMenu">
            <span>🎨</span>
            <span>AI绘画</span>
          </router-link>
          <router-link to="/gallery" class="nav-item" @click="closeMobileMenu">
            <span>🖼️</span>
            <span>作品展示</span>
          </router-link>
          <router-link to="/pricing" class="nav-item pricing-link" @click="closeMobileMenu">
            <span>💰</span>
            <span>定价方案</span>
            <div class="pricing-badge">HOT</div>
          </router-link>

          <!-- 侧边菜单底部 -->
          <div class="sidebar-footer">
            <div v-if="safeIsLoggedIn" class="sidebar-user">
              <img :src="safeUserAvatar" :alt="safeUserName" class="user-avatar-small" />
              <div class="user-info">
                <div class="user-name">{{ safeUserName }}</div>
                <div class="user-email">{{ safeUserEmail || '用户' }}</div>
              </div>
            </div>
            <div v-else class="sidebar-auth">
              <router-link to="/login" class="auth-link" @click="closeMobileMenu">
                <span>🔑</span>
                <span>登录</span>
              </router-link>
              <router-link to="/register" class="auth-link primary" @click="closeMobileMenu">
                <span>✨</span>
                <span>注册</span>
              </router-link>
            </div>
          </div>
        </div>

        <!-- 右侧操作区 -->
        <div class="nav-actions">
          <!-- 管理员导航 -->
          <AdminNavigation v-if="safeIsAdmin" />

          <!-- 搜索按钮 -->
          <button class="action-btn search-btn" @click="toggleSearch">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19S2 15.194 2 10.5 5.806 2 10.5 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>

          <!-- 用户菜单 -->
          <div v-if="safeIsLoggedIn" class="user-menu" @click.stop>
            <div class="user-avatar" @click="toggleUserMenu">
              <img
                :src="safeUserAvatar"
                :alt="safeUserName"
                @error="handleAvatarError"
                @load="handleAvatarLoad"
              />
              <div class="user-name">{{ safeUserName }}</div>
              <svg class="dropdown-icon" :class="{ active: userMenuOpen }" width="12" height="12" viewBox="0 0 24 24" fill="none">
                <path d="M6 9l6 6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>

            <!-- 用户下拉菜单 -->
            <div class="user-dropdown" :class="{ active: userMenuOpen }">
              <div class="user-info">
                <img
                  :src="safeUserAvatar"
                  :alt="safeUserName"
                  class="dropdown-avatar"
                  @error="handleAvatarError"
                  @load="handleAvatarLoad"
                />
                <div class="user-details">
                  <div class="user-name">{{ safeUserName }}</div>
                  <div class="user-role">{{ safeUserRole === 'admin' ? '管理员' : '普通用户' }}</div>
                </div>
              </div>

              <div class="menu-divider"></div>

              <div class="menu-items">
                <button class="menu-item" @click="handleUserCommand('profile')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  个人资料
                </button>

                <button class="menu-item" @click="handleUserCommand('settings')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  设置
                </button>

                <div class="menu-divider"></div>

                <button class="menu-item logout-item" @click="handleUserCommand('logout')">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4M16 17l5-5-5-5M21 12H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  退出登录
                </button>
              </div>
            </div>
          </div>

          <!-- 登录/注册按钮 -->
          <div v-else class="auth-buttons">
            <router-link to="/register" class="auth-btn register-btn">
              <span class="btn-text">现在加入</span>
              <span class="btn-badge">简单免费</span>
            </router-link>
            <router-link to="/login" class="auth-btn login-btn">登录</router-link>
          </div>
        </div>

        <!-- 移动端菜单按钮 -->
        <button
          class="mobile-menu-btn"
          @click="toggleMobileMenu"
          :class="{ active: mobileMenuOpen }"
          :title="`菜单${mobileMenuOpen ? '已打开' : '已关闭'}`"
        >
          <div class="hamburger-icon">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </button>
      </nav>

      <!-- 移动端菜单遮罩层 -->
      <div
        v-if="mobileMenuOpen"
        class="mobile-menu-overlay"
        @click="closeMobileMenu"
      ></div>

      <!-- 调试信息 (仅开发环境) -->
      <div v-if="isDev" class="debug-panel">
        <div>移动端菜单: {{ mobileMenuOpen ? '开启' : '关闭' }}</div>
        <div>设备类型: {{ safeDevice }}</div>
        <div>屏幕宽度: {{ safeScreenSize }}</div>
        <div>是否移动端: {{ safeIsMobile }}</div>
      </div>

      <!-- 搜索覆盖层 -->
      <div class="search-overlay" :class="{ active: searchOpen }" @click="closeSearch">
        <div class="search-container" @click.stop>
          <div class="search-input-wrapper">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M21 21L16.514 16.506M19 10.5C19 15.194 15.194 19 10.5 19S2 15.194 2 10.5 5.806 2 10.5 2 19 5.806 19 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            <input
              ref="searchInput"
              v-model="searchKeyword"
              type="text"
              placeholder="搜索作品、用户..."
              @keyup.enter="handleSearch"
              @keyup.esc="closeSearch"
            />
            <button class="search-close" @click="closeSearch">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区域 -->
    <main class="app-main">
      <router-view v-slot="{ Component }">
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore, useUserStore } from '@/stores'
import AdminNavigation from '@/components/admin/AdminNavigation.vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 安全的计算属性，确保store已初始化
const safeUserStore = computed(() => {
  try {
    // 确保userStore存在且已初始化
    if (!userStore) {
      return {
        isLoggedIn: false,
        userName: '游客',
        userAvatar: '/default-avatar.svg',
        userRole: 'user',
        isAdmin: false
      }
    }

    return {
      isLoggedIn: userStore.isLoggedIn ?? false,
      userName: userStore.userName ?? '游客',
      userAvatar: userStore.userAvatar ?? '/default-avatar.svg',
      userRole: userStore.userRole ?? 'user',
      isAdmin: userStore.isAdmin ?? false
    }
  } catch (error) {
    console.warn('获取用户信息时出错:', error)
    return {
      isLoggedIn: false,
      userName: '游客',
      userAvatar: '/default-avatar.svg',
      userRole: 'user',
      isAdmin: false
    }
  }
})

const searchKeyword = ref('')
const searchOpen = ref(false)
const searchInput = ref(null)
const mobileMenuOpen = ref(false)
const userMenuOpen = ref(false)

// 开发环境标识
const isDev = import.meta.env.DEV

// 从safeUserStore中提取单独的计算属性
const safeIsLoggedIn = computed(() => safeUserStore.value.isLoggedIn)
const safeUserName = computed(() => safeUserStore.value.userName)
const safeUserAvatar = computed(() => safeUserStore.value.userAvatar)
const safeUserRole = computed(() => safeUserStore.value.userRole)
const safeIsAdmin = computed(() => safeUserStore.value.isAdmin)

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/search',
      query: { q: searchKeyword.value.trim() }
    })
    closeSearch()
  }
}

const toggleSearch = () => {
  searchOpen.value = !searchOpen.value
  if (searchOpen.value) {
    nextTick(() => {
      searchInput.value?.focus()
    })
  }
}

const closeSearch = () => {
  searchOpen.value = false
  searchKeyword.value = ''
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
  console.log('移动端菜单状态:', mobileMenuOpen.value)
}

const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}

const handleMenuOverlayClick = (e) => {
  // 如果点击的是菜单背景（不是菜单项），则关闭菜单
  if (e.target.classList.contains('nav-menu-open')) {
    closeMobileMenu()
  }
}

const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
}

const closeUserMenu = () => {
  userMenuOpen.value = false
}

const handleUserCommand = async (command) => {
  closeUserMenu() // 执行命令后关闭菜单

  try {
    switch (command) {
      case 'profile':
        router.push('/profile')
        break
      case 'settings':
        router.push('/settings')
        break
      case 'logout':
        if (userStore && userStore.logout) {
          await userStore.logout()
          router.push('/login')
        }
        break
    }
  } catch (error) {
    console.warn('Layout组件: 用户命令执行失败:', error)
  }
}

// 头像加载错误处理
const handleAvatarError = (event) => {
  console.warn('头像加载失败，使用默认头像')
  // 如果不是默认头像，尝试使用默认头像
  if (!event.target.src.includes('default-avatar.svg')) {
    event.target.src = '/default-avatar.svg'
  } else {
    // 如果默认头像也失败，使用data URL的SVG
    event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSIyMCIgZmlsbD0iIzYzNjZmMSIvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTUiIHI9IjYiIGZpbGw9IndoaXRlIi8+CiAgPHBhdGggZD0iTTcgMzJDNyAyNi40NzcyIDExLjQ3NzIgMjIgMTcgMjJIMjNDMjguNTIyOCAyMiAzMyAyNi40NzcyIDMzIDMyVjM1QzMzIDM2LjY1NjkgMzEuNjU2OSAzOCAzMCAzOEgxMEM4LjM0MzE1IDM4IDcgMzYuNjU2OSA3IDM1VjMyWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+'
  }
}

// 头像加载成功处理
const handleAvatarLoad = (event) => {
  console.log('头像加载成功:', event.target.src)
}

onMounted(async () => {
  // 安全地初始化应用
  try {
    if (appStore && appStore.initApp) {
      await appStore.initApp()
    }
  } catch (error) {
    console.warn('Layout组件: 应用初始化失败:', error)
  }

  const handleKeydown = (e) => {
    if (e.key === 'Escape') {
      closeSearch()
      closeMobileMenu()
      closeUserMenu()
    }
  }

  const handleClickOutside = (e) => {
    // 点击外部关闭用户菜单
    if (!e.target.closest('.user-menu')) {
      closeUserMenu()
    }
    // 点击外部关闭移动端菜单
    if (!e.target.closest('.nav-menu') && !e.target.closest('.mobile-menu-btn')) {
      closeMobileMenu()
    }
  }

  try {
    document.addEventListener('keydown', handleKeydown)
    document.addEventListener('click', handleClickOutside)
  } catch (error) {
    console.warn('Layout组件: 事件监听器注册失败:', error)
  }
})

onUnmounted(() => {
  try {
    if (appStore && appStore.cleanupApp) {
      appStore.cleanupApp()
    }
  } catch (error) {
    console.warn('Layout组件: 应用清理失败:', error)
  }
})
</script>

<style lang="scss" scoped>
// 变量已通过Vite配置全局导入，无需手动导入

.app-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafafa;
}

.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  height: 64px;

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .nav-brand {
    .brand-link {
      display: flex;
      align-items: center;
      gap: 12px;
      text-decoration: none;
      color: #1d1d1f;
      font-weight: 600;
      font-size: 18px;
      transition: opacity 0.2s ease;

      &:hover {
        opacity: 0.8;
      }
    }

    .brand-icon {
      position: relative;
      width: 32px;
      height: 32px;
    }

    .ai-logo {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .logo-circle {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 12px;
          height: 12px;
          background: white;
          border-radius: 50%;
        }
      }

      .logo-spark {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 8px;
        height: 8px;
        background: #FF9500;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }
    }

    .brand-text {
      background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: 32px;

    // 在移动端隐藏桌面端菜单，改为侧边导航栏
    @media (max-width: 768px) {
      // 默认状态：隐藏在左侧
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      bottom: 0 !important;
      width: 280px !important;
      max-width: 80vw !important;
      flex-direction: column !important;
      justify-content: flex-start !important;
      align-items: stretch !important;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      backdrop-filter: blur(20px) !important;
      -webkit-backdrop-filter: blur(20px) !important;
      z-index: 10000 !important;
      padding: 0 !important;
      gap: 0 !important;
      transform: translateX(-100%) !important;
      transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3) !important;
      display: flex !important;
      overflow-y: auto !important;
      -webkit-overflow-scrolling: touch !important;

      // 当菜单打开时，滑入视图
      &.nav-menu-open {
        transform: translateX(0) !important;
        transform: translateX(0) !important;
        opacity: 1 !important;
        visibility: visible !important;

        // 添加关闭按钮
        &::before {
          content: '✕';
          position: absolute;
          top: 32px;
          right: 32px;
          font-size: 24px;
          color: white;
          cursor: pointer;
          z-index: 10001;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          transition: all 0.3s ease;
        }

        &::before:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: scale(1.1);
        }

        // 侧边菜单头部
        .sidebar-header {
          height: 80px !important;
          background: rgba(255, 255, 255, 0.1) !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
          margin-bottom: 20px !important;
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
          padding: 0 24px !important;

          .sidebar-brand {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;

            .brand-icon {
              width: 32px !important;
              height: 32px !important;

              .ai-logo {
                width: 32px !important;
                height: 32px !important;

                .logo-circle {
                  width: 32px !important;
                  height: 32px !important;
                  background: white !important;
                  border-radius: 50% !important;
                  position: relative !important;

                  &::after {
                    content: '🤖' !important;
                    position: absolute !important;
                    top: 50% !important;
                    left: 50% !important;
                    transform: translate(-50%, -50%) !important;
                    font-size: 16px !important;
                  }
                }
              }
            }

            .brand-text {
              font-size: 18px !important;
              font-weight: 600 !important;
              color: white !important;
            }
          }

          .sidebar-close {
            background: none !important;
            border: none !important;
            color: white !important;
            cursor: pointer !important;
            padding: 8px !important;
            border-radius: 4px !important;
            transition: background-color 0.2s ease !important;

            &:hover {
              background: rgba(255, 255, 255, 0.1) !important;
            }

            svg {
              width: 20px !important;
              height: 20px !important;
            }
          }
        }

        .nav-item {
          width: 100% !important;
          max-width: none !important;
          text-align: left !important;
          padding: 16px 24px !important;
          border-radius: 0 !important;
          margin: 0 !important;
          font-size: 16px !important;
          font-weight: 500 !important;
          color: white !important;
          background: transparent !important;
          border: none !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
          transition: all 0.2s ease !important;
          text-decoration: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: flex-start !important;
          gap: 16px !important;
          // 优化触摸体验
          -webkit-tap-highlight-color: transparent !important;
          touch-action: manipulation !important;

          span:first-child {
            font-size: 20px !important;
            width: 24px !important;
            text-align: center !important;
          }

          &:hover,
          &:active {
            background: rgba(255, 255, 255, 0.15) !important;
            padding-left: 32px !important;
          }

          &.router-link-active {
            background: rgba(255, 255, 255, 0.2) !important;
            font-weight: 600 !important;
            position: relative !important;

            &::after {
              content: '' !important;
              position: absolute !important;
              right: 0 !important;
              top: 0 !important;
              bottom: 0 !important;
              width: 4px !important;
              background: white !important;
            }
          }

          // 移动端触摸反馈
          @media (max-width: 768px) {
            &:active {
              background: rgba(255, 255, 255, 0.25) !important;
            }
          }

          &.router-link-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
          }

          &.pricing-link {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
            color: #8b4513 !important;

            &:hover {
              background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%) !important;
              transform: translateY(-2px) scale(1.02) !important;
            }
          }
        }

        // 侧边菜单底部
        .sidebar-footer {
          margin-top: auto !important;
          padding: 20px 24px !important;
          border-top: 1px solid rgba(255, 255, 255, 0.2) !important;

          .sidebar-user {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;

            .user-avatar-small {
              width: 40px !important;
              height: 40px !important;
              border-radius: 50% !important;
              border: 2px solid rgba(255, 255, 255, 0.3) !important;
            }

            .user-info {
              flex: 1 !important;

              .user-name {
                color: white !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                margin-bottom: 2px !important;
              }

              .user-email {
                color: rgba(255, 255, 255, 0.7) !important;
                font-size: 12px !important;
              }
            }
          }

          .sidebar-auth {
            display: flex !important;
            flex-direction: column !important;
            gap: 8px !important;

            .auth-link {
              display: flex !important;
              align-items: center !important;
              gap: 12px !important;
              padding: 12px 16px !important;
              border-radius: 8px !important;
              text-decoration: none !important;
              color: white !important;
              font-size: 14px !important;
              font-weight: 500 !important;
              background: rgba(255, 255, 255, 0.1) !important;
              border: 1px solid rgba(255, 255, 255, 0.2) !important;
              transition: all 0.2s ease !important;

              &:hover {
                background: rgba(255, 255, 255, 0.2) !important;
              }

              &.primary {
                background: rgba(255, 255, 255, 0.9) !important;
                color: #667eea !important;
                font-weight: 600 !important;

                &:hover {
                  background: white !important;
                }
              }

              span:first-child {
                font-size: 16px !important;
              }
            }
          }
        }
      }
    }

    .nav-item {
      color: #1d1d1f;
      text-decoration: none;
      font-size: 16px;
      font-weight: 400;
      padding: 8px 16px;
      border-radius: 20px;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        background: rgba(0, 122, 255, 0.1);
        color: #007AFF;
      }

      &.router-link-active {
        background: #007AFF;
        color: white;
        font-weight: 500;
      }

      &.pricing-link {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        color: white;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        &:hover {
          background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }

        .pricing-badge {
          background: #ff6b6b;
          color: white;
          font-size: 10px;
          font-weight: 700;
          padding: 2px 6px;
          border-radius: 8px;
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .nav-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .action-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: #1d1d1f;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.05);
    }
  }

  .user-menu {
    position: relative;

    .user-avatar {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      border-radius: 20px;
      cursor: pointer;
      transition: all 0.2s ease;
      background: rgba(0, 0, 0, 0.05);

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }

      img {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        object-fit: cover;
        background-color: #f3f4f6;
        border: 1px solid #e5e7eb;
        transition: all 0.2s ease;

        &:hover {
          border-color: #d1d5db;
        }
      }

      .user-name {
        font-size: 14px;
        font-weight: 500;
        color: #1d1d1f;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .dropdown-icon {
        transition: transform 0.2s ease;
        color: #86868b;

        &.active {
          transform: rotate(180deg);
        }
      }
    }

    .user-dropdown {
      position: absolute;
      top: calc(100% + 8px);
      right: 0;
      width: 280px;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      border: 1px solid rgba(0, 0, 0, 0.1);
      opacity: 0;
      visibility: hidden;
      transform: translateY(-10px);
      transition: all 0.2s ease;
      z-index: 1000;

      &.active {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      .user-info {
        padding: 16px;
        display: flex;
        align-items: center;
        gap: 12px;

        .dropdown-avatar {
          width: 48px;
          height: 48px;
          border-radius: 50%;
          object-fit: cover;
          background-color: #f3f4f6;
          border: 2px solid #e5e7eb;
          transition: all 0.2s ease;
        }

        .user-details {
          flex: 1;

          .user-name {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 2px;
          }

          .user-role {
            font-size: 12px;
            color: #86868b;
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
            padding: 2px 8px;
            border-radius: 10px;
            display: inline-block;
          }
        }
      }

      .menu-divider {
        height: 1px;
        background: rgba(0, 0, 0, 0.1);
        margin: 0 16px;
      }

      .menu-items {
        padding: 8px 0;

        .menu-item {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px 16px;
          border: none;
          background: none;
          color: #1d1d1f;
          font-size: 14px;
          cursor: pointer;
          transition: background 0.2s ease;
          text-align: left;

          &:hover {
            background: rgba(0, 0, 0, 0.05);
          }

          &.logout-item {
            color: #ff3b30;

            &:hover {
              background: rgba(255, 59, 48, 0.1);
            }
          }

          svg {
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .auth-buttons {
    display: flex;
    gap: 12px;
    align-items: center;

    .auth-btn {
      padding: 8px 20px;
      border-radius: 20px;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
      position: relative;
      // 移动端触摸优化
      -webkit-tap-highlight-color: transparent;
      touch-action: manipulation;

      &.register-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 10px 24px;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

        .btn-text {
          font-weight: 600;
        }

        .btn-badge {
          background: rgba(255, 255, 255, 0.2);
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          backdrop-filter: blur(10px);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.login-btn {
        background: rgba(255, 255, 255, 0.1);
        color: $text-color-primary;
        border: 1px solid rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .mobile-menu-btn {
    display: none; // PC端完全隐藏
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1001;

    // 只在移动端显示
    @media (max-width: 768px) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: none;
      border: none;
      cursor: pointer;
      border-radius: 8px;
      transition: background-color 0.2s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
      }

      &:active {
        background: rgba(0, 0, 0, 0.1);
      }

      .hamburger-icon {
        width: 24px;
        height: 18px;
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        span {
          display: block;
          width: 100%;
          height: 2px;
          background: #1f2937;
          border-radius: 1px;
          transition: all 0.3s ease;
          transform-origin: center;
        }
      }

      &.active .hamburger-icon {
        span:nth-child(1) {
          transform: rotate(45deg) translate(6px, 6px);
        }

        span:nth-child(2) {
          opacity: 0;
        }

        span:nth-child(3) {
          transform: rotate(-45deg) translate(6px, -6px);
        }
      }
    }
  }

  .search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;

    &.active {
      opacity: 1;
      visibility: visible;
    }

    .search-container {
      position: absolute;
      top: 80px;
      left: 50%;
      transform: translateX(-50%);
      width: 90%;
      max-width: 600px;
      background: white;
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      transform: translateX(-50%) translateY(-20px);
      transition: transform 0.3s ease;
    }

    &.active .search-container {
      transform: translateX(-50%) translateY(0);
    }

    .search-input-wrapper {
      display: flex;
      align-items: center;
      padding: 20px 24px;
      gap: 16px;

      .search-icon {
        color: #8e8e93;
        flex-shrink: 0;
      }

      input {
        flex: 1;
        border: none;
        outline: none;
        font-size: 18px;
        color: #1d1d1f;
        background: transparent;

        &::placeholder {
          color: #8e8e93;
        }
      }

      .search-close {
        width: 32px;
        height: 32px;
        border: none;
        background: rgba(142, 142, 147, 0.1);
        border-radius: 50%;
        color: #8e8e93;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(142, 142, 147, 0.2);
        }
      }
    }
  }
}

.app-main {
  flex: 1;
  background: #fafafa;
  overflow-y: auto;
  height: calc(100vh - 64px);
  padding-top: 64px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 调试面板样式
.debug-panel {
  position: fixed;
  top: 80px;
  right: 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9998;
  min-width: 200px;

  div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  @media (max-width: 768px) {
    top: 70px;
    right: 8px;
    font-size: 11px;
    min-width: 180px;
  }
}

// 移动端菜单遮罩层
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 现代化移动端适配
@media (max-width: 768px) {
  .app-header {
    height: 56px; // 减少移动端头部高度

    .nav-container {
      padding: 0 16px; // 减少左右边距
      height: 56px;
    }

    .nav-brand {
      .brand-text {
        font-size: 16px; // 减小字体
      }

      .brand-icon {
        width: 32px;
        height: 32px;

        .ai-logo {
          width: 32px;
          height: 32px;

          .logo-circle {
            width: 32px;
            height: 32px;
          }
        }
      }
    }

    .nav-actions {
      gap: 8px; // 减少按钮间距

      .auth-btn {
        padding: 8px 16px; // 减小按钮内边距
        font-size: 14px;
        height: 36px;

        &.signup-btn {
          padding: 8px 12px;
        }

        &.login-btn {
          padding: 6px 12px;
        }
      }
    }





    .nav-actions {
      display: none; // 移动端隐藏导航操作区
    }

    .search-overlay {
      .search-container {
        width: 95%;
        top: 80px;
        padding: $spacing-lg;

        .search-input {
          font-size: 16px; // 防止iOS缩放
        }
      }
    }

    .user-menu {
      .user-avatar {
        padding: 4px 8px;

        .user-name {
          display: none;
        }

        img {
          width: 32px;
          height: 32px;
        }
      }

      .user-dropdown {
        width: calc(100vw - 32px);
        max-width: 320px;
        right: -8px;

        .user-info {
          padding: $spacing-lg;

          .dropdown-avatar {
            width: 56px;
            height: 56px;
          }
        }

        .menu-item {
          padding: $spacing-md $spacing-lg;
          font-size: 16px;
        }
      }
    }
  }
}

// 超小屏幕优化
@media (max-width: 480px) {
  .app-header {
    height: 50px; // 进一步减少高度

    .nav-container {
      padding: 0 12px;
      height: 50px;
    }

    // 超小屏幕侧边菜单优化
    .nav-menu {
      width: 260px !important;
      max-width: 85vw !important;

      .sidebar-header {
        height: 70px !important;
        padding: 0 16px !important;

        .sidebar-brand .brand-text {
          font-size: 16px !important;
        }
      }

      .nav-item {
        padding: 14px 20px !important;
        font-size: 15px !important;

        span:first-child {
          font-size: 18px !important;
        }
      }

      .sidebar-footer {
        padding: 16px 20px !important;

        .sidebar-user .user-avatar-small {
          width: 36px !important;
          height: 36px !important;
        }

        .sidebar-auth .auth-link {
          padding: 10px 14px !important;
          font-size: 13px !important;
        }
      }
    }

    .nav-brand {
      .brand-text {
        display: none; // 隐藏品牌文字
      }

      .brand-icon {
        width: 28px;
        height: 28px;

        .ai-logo {
          width: 28px;
          height: 28px;

          .logo-circle {
            width: 28px;
            height: 28px;
          }
        }
      }
    }

    .nav-actions {
      gap: 4px;

      .auth-btn {
        font-size: 12px;
        padding: 6px 8px;
        height: 30px;

        &.signup-btn {
          padding: 6px 6px;
        }

        &.login-btn {
          padding: 5px 6px;
        }
      }
    }

    .mobile-menu-btn {
      width: 32px;
      height: 32px;

      .hamburger-icon {
        width: 18px;
        height: 13px;

        span {
          height: 1.5px;
        }
      }
    }

    .nav-menu {
      padding: 20px 12px;

      .nav-item {
        padding: 14px 16px;
        font-size: 15px;
        max-width: 260px;
      }
    }

    .user-menu {
      .user-avatar {
        padding: 4px 6px;
        height: 30px;

        img {
          width: 22px;
          height: 22px;
        }
      }

      .user-dropdown {
        width: calc(100vw - 16px);
        right: -4px;
      }
    }
  }
}

// 移动端菜单动画
@keyframes slideInUp {
  to {
    transform: translateY(0);
    opacity: 1;
  }
}



// 主要内容区域移动端适配
@media (max-width: 768px) {
  .main-content {
    padding: $spacing-lg $spacing-md;
    margin-top: 56px; // 为移动端头部留出空间
  }

  .page-title {
    font-size: 24px;
    margin-bottom: $spacing-lg;
    text-align: center;
  }

@media (max-width: 480px) {
  .main-content {
    padding: $spacing-md $spacing-sm;
    margin-top: 50px; // 为超小屏幕头部留出空间
  }

  .page-title {
    font-size: 20px;
    margin-bottom: $spacing-md;
  }
}

  .features-grid {
    grid-template-columns: 1fr;
    gap: $spacing-lg;
  }

  .feature-card {
    padding: $spacing-lg;
    text-align: center;
  }

  .btn-primary {
    width: 100%;
    padding: $spacing-md $spacing-lg;
    font-size: 16px;
  }

  .form-group {
    margin-bottom: $spacing-md;
  }

  .form-input {
    padding: $spacing-md;
    font-size: 16px;
    width: 100%;
    box-sizing: border-box;
  }

  .modal-content {
    margin: $spacing-md;
    max-height: calc(100vh - #{$spacing-xl});
    overflow-y: auto;
    border-radius: 12px;
  }
}

// 平板适配 (768px - 1024px)
@media (min-width: 769px) and (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-content {
    padding: $spacing-xl $spacing-lg;
  }

  .page-title {
    font-size: 32px;
  }
}

// 大屏幕适配 (大于1200px)
@media (min-width: 1200px) {
  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
  }

  .main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: $spacing-xl $spacing-lg;
  }

  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .page-title {
    font-size: 36px;
  }
}

</style>