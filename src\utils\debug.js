/**
 * 调试工具函数
 */

// 检查应用状态
export function checkAppStatus() {
  const status = {
    timestamp: new Date().toISOString(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    viewport: {
      width: window.innerWidth,
      height: window.innerHeight
    },
    localStorage: {
      available: typeof Storage !== 'undefined',
      items: {}
    },
    stores: {},
    errors: []
  }

  // 检查localStorage
  if (status.localStorage.available) {
    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('ai_creative_')) {
          status.localStorage.items[key] = localStorage.getItem(key)?.substring(0, 100) + '...'
        }
      }
    } catch (error) {
      status.errors.push(`localStorage检查失败: ${error.message}`)
    }
  }

  // 检查Stores状态
  try {
    if (window.__VUE_DEVTOOLS_GLOBAL_HOOK__) {
      status.vueDevtools = true
    }
  } catch (error) {
    status.errors.push(`Vue DevTools检查失败: ${error.message}`)
  }

  return status
}

// 清理应用状态
export function cleanupAppState() {
  try {
    // 清理localStorage
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.startsWith('ai_creative_') || key.includes('chat') || key.includes('drawing'))) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => localStorage.removeItem(key))
    
    // 清理sessionStorage
    sessionStorage.clear()
    
    console.log('✅ 应用状态已清理')
    return true
  } catch (error) {
    console.error('❌ 清理应用状态失败:', error)
    return false
  }
}

// 显示调试信息
export function showDebugInfo() {
  const status = checkAppStatus()
  
  console.group('🔍 应用调试信息')
  console.log('📊 应用状态:', status)
  console.log('🌐 环境变量:', import.meta.env)
  console.log('📦 依赖版本:', {
    vue: '3.4.0',
    elementPlus: '2.4.4',
    pinia: '2.1.7',
    vueRouter: '4.2.5'
  })
  console.groupEnd()
  
  return status
}

// 修复常见问题
export function fixCommonIssues() {
  const fixes = []
  
  try {
    // 修复1: 清理损坏的localStorage数据
    const corruptedKeys = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        try {
          const value = localStorage.getItem(key)
          if (value && (value.startsWith('{') || value.startsWith('['))) {
            JSON.parse(value) // 尝试解析JSON
          }
        } catch (error) {
          corruptedKeys.push(key)
        }
      }
    }
    
    if (corruptedKeys.length > 0) {
      corruptedKeys.forEach(key => localStorage.removeItem(key))
      fixes.push(`清理了 ${corruptedKeys.length} 个损坏的localStorage项`)
    }
    
    // 修复2: 重置错误的环境变量
    if (!import.meta.env.VITE_APP_TITLE) {
      fixes.push('检测到缺失的环境变量')
    }
    
    console.log('🔧 修复完成:', fixes)
    return fixes
  } catch (error) {
    console.error('❌ 修复过程出错:', error)
    return []
  }
}

// 清理调试样式
export function cleanupDebugStyles() {
  try {
    // 移除所有可能的调试边框
    const elements = document.querySelectorAll('*')
    elements.forEach(el => {
      const style = el.getAttribute('style')
      if (style) {
        // 移除红色边框
        if (style.includes('border') && (style.includes('red') || style.includes('#ff0000'))) {
          el.style.border = 'none'
        }
        // 移除调试背景
        if (style.includes('background') && (style.includes('red') || style.includes('#ff0000'))) {
          el.style.background = 'transparent'
        }
      }

      // 移除outline
      el.style.outline = 'none'
    })

    // 隐藏Vue DevTools相关元素
    const devtoolsElements = document.querySelectorAll('[class*="__vue-devtools"], [class*="devtools"]')
    devtoolsElements.forEach(el => {
      el.style.display = 'none'
    })

    console.log('✅ 调试样式已清理')
    return true
  } catch (error) {
    console.error('❌ 清理调试样式失败:', error)
    return false
  }
}

// 隐藏开发者工具相关显示
export function hideDevToolsUI() {
  try {
    // 隐藏可能的开发者工具UI
    const style = document.createElement('style')
    style.textContent = `
      /* 隐藏所有可能的调试UI */
      [class*="devtools"],
      [class*="debug"],
      [class*="inspector"],
      [id*="devtools"],
      [id*="debug"],
      [id*="inspector"] {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
      }

      /* 移除调试边框 */
      * {
        outline: none !important;
      }

      /* 确保主容器正常显示 */
      #app {
        border: none !important;
        outline: none !important;
      }
    `
    document.head.appendChild(style)

    console.log('✅ 开发者工具UI已隐藏')
    return true
  } catch (error) {
    console.error('❌ 隐藏开发者工具UI失败:', error)
    return false
  }
}

// 导出到全局对象，方便在控制台调用
if (typeof window !== 'undefined') {
  window.debugApp = {
    checkStatus: checkAppStatus,
    cleanup: cleanupAppState,
    showInfo: showDebugInfo,
    fix: fixCommonIssues,
    cleanupStyles: cleanupDebugStyles,
    hideDevTools: hideDevToolsUI
  }

  // 自动清理调试样式
  setTimeout(() => {
    cleanupDebugStyles()
    hideDevToolsUI()
  }, 1000)

  console.log('🛠️ 调试工具已加载，可在控制台使用 window.debugApp')
}
