import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { APP_CONFIG, DEFAULT_SETTINGS } from '@/config'
// import { loginApi, logoutApi, getUserInfoApi, updateUserInfoApi } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(getToken() || '')
  const userInfo = ref(null)
  const settings = ref({ ...DEFAULT_SETTINGS })
  const loading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const userName = computed(() => userInfo.value?.username || '游客')
  const userAvatar = computed(() => userInfo.value?.avatar || '/default-avatar.svg')
  const userRole = computed(() => userInfo.value?.role || 'user')
  const isAdmin = computed(() => userRole.value === 'admin')
  const isVip = computed(() => userRole.value === 'vip')

  // 获取本地存储的用户数据库
  const getLocalUserDatabase = () => {
    try {
      const usersData = localStorage.getItem('ai_creative_users_db')
      return usersData ? JSON.parse(usersData) : []
    } catch (error) {
      console.error('获取本地用户数据库失败:', error)
      return []
    }
  }

  // 保存用户到本地数据库
  const saveUserToDatabase = (userData) => {
    try {
      const users = getLocalUserDatabase()
      users.push(userData)
      localStorage.setItem('ai_creative_users_db', JSON.stringify(users))
      return true
    } catch (error) {
      console.error('保存用户到本地数据库失败:', error)
      return false
    }
  }

  // 检查用户名或邮箱是否已存在
  const checkUserExists = (username, email) => {
    const users = getLocalUserDatabase()
    return users.some(user =>
      user.username === username || user.email === email
    )
  }

  // 注册 - 前端模拟
  const register = async (registerForm) => {
    console.log('用户store: 开始注册', registerForm)

    try {
      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1500))

      // 检查用户名或邮箱是否已存在
      if (checkUserExists(registerForm.username, registerForm.email)) {
        ElMessage({
          message: '用户名或邮箱已存在，请使用其他用户名或邮箱',
          type: 'error',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        return { success: false, message: '用户名或邮箱已存在' }
      }

      // 创建新用户数据
      const newUser = {
        id: Date.now(),
        username: registerForm.username,
        email: registerForm.email,
        password: registerForm.password, // 实际项目中应该加密
        avatar: '/default-avatar.svg',
        role: 'user',
        nickname: registerForm.username,
        createdAt: new Date().toISOString(),
        lastLoginAt: null,
        isActive: true,
        profile: {
          bio: '',
          location: '',
          website: '',
          birthday: null,
          gender: null
        },
        preferences: {
          theme: 'light',
          language: 'zh-CN',
          notifications: {
            email: true,
            push: true,
            marketing: false
          }
        }
      }

      // 保存用户到本地数据库
      if (saveUserToDatabase(newUser)) {
        console.log('用户store: 注册成功', newUser)
        ElMessage({
          message: '注册成功！请登录您的账户',
          type: 'success',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        return { success: true, user: newUser }
      } else {
        throw new Error('保存用户数据失败')
      }
    } catch (error) {
      console.error('用户store: 注册错误:', error)
      ElMessage({
        message: '注册失败，请稍后重试',
        type: 'error',
        duration: 4000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
      return { success: false, message: '注册失败' }
    } finally {
      loading.value = false
    }
  }

  // 登录 - 前端模拟
  const login = async (loginForm) => {
    console.log('用户store: 开始登录', loginForm)

    try {
      loading.value = true

      // 模拟API调用延迟
      console.log('用户store: 模拟API延迟')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取本地用户数据库
      const localUsers = getLocalUserDatabase()

      // 默认管理员账户
      const defaultUsers = [
        { username: 'admin', password: '123456', role: 'admin', email: '<EMAIL>' },
        { username: 'user', password: '123456', role: 'user', email: '<EMAIL>' },
        { username: 'test', password: '123456', role: 'user', email: '<EMAIL>' }
      ]

      // 合并默认用户和注册用户
      const allUsers = [...defaultUsers, ...localUsers]

      console.log('用户store: 查找用户', { username: loginForm.username, password: loginForm.password })

      const user = allUsers.find(u =>
        (u.username === loginForm.username || u.email === loginForm.username) &&
        u.password === loginForm.password
      )

      console.log('用户store: 找到的用户', user)

      if (user) {
        // 生成模拟token
        const mockToken = 'mock_token_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)

        // 生成模拟用户信息
        const mockUserInfo = {
          id: user.id || Date.now(),
          username: user.username,
          email: user.email,
          avatar: user.avatar || '/default-avatar.svg',
          role: user.role || 'user',
          nickname: user.nickname || user.username,
          createdAt: user.createdAt || new Date().toISOString(),
          lastLoginAt: new Date().toISOString(),
          profile: user.profile || {
            bio: '',
            location: '',
            website: '',
            birthday: null,
            gender: null
          },
          preferences: user.preferences || {
            theme: 'light',
            language: 'zh-CN',
            notifications: {
              email: true,
              push: true,
              marketing: false
            }
          }
        }

        console.log('用户store: 设置token和用户信息', { token: mockToken, userInfo: mockUserInfo })

        token.value = mockToken
        userInfo.value = mockUserInfo

        // 保存token到本地存储
        setToken(token.value)

        // 保存用户信息到本地存储
        localStorage.setItem(APP_CONFIG.storage.userKey, JSON.stringify(userInfo.value))

        // 更新用户最后登录时间（如果是注册用户）
        if (user.id && localUsers.some(u => u.id === user.id)) {
          const updatedUsers = localUsers.map(u =>
            u.id === user.id ? { ...u, lastLoginAt: new Date().toISOString() } : u
          )
          localStorage.setItem('ai_creative_users_db', JSON.stringify(updatedUsers))
        }

        // 如果选择了记住我，保存登录信息
        if (loginForm.rememberMe) {
          localStorage.setItem('ai_creative_remember_me', JSON.stringify({
            username: loginForm.username,
            timestamp: Date.now()
          }))
        } else {
          localStorage.removeItem('ai_creative_remember_me')
        }

        console.log('用户store: 登录成功')
        return { success: true }
      } else {
        console.log('用户store: 用户名或密码错误')
        ElMessage({
          message: '用户名或密码错误，请检查后重试',
          type: 'error',
          duration: 4000,
          showClose: true,
          customClass: 'high-contrast-message',
          center: true
        })
        return { success: false, message: '用户名或密码错误' }
      }
    } catch (error) {
      console.error('用户store: 登录错误:', error)
      ElMessage({
        message: '登录失败，请稍后重试',
        type: 'error',
        duration: 4000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
      return { success: false, message: '登录失败' }
    } finally {
      loading.value = false
    }
  }

  // 获取记住我信息
  const getRememberMeInfo = () => {
    try {
      const rememberData = localStorage.getItem('ai_creative_remember_me')
      if (rememberData) {
        const data = JSON.parse(rememberData)
        // 检查是否过期（30天）
        const thirtyDays = 30 * 24 * 60 * 60 * 1000
        if (Date.now() - data.timestamp < thirtyDays) {
          return data.username
        } else {
          localStorage.removeItem('ai_creative_remember_me')
        }
      }
      return null
    } catch (error) {
      console.error('获取记住我信息失败:', error)
      return null
    }
  }

  // 更新用户信息
  const updateUserInfo = async (updates) => {
    try {
      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 更新当前用户信息
      userInfo.value = { ...userInfo.value, ...updates }

      // 保存到本地存储
      localStorage.setItem(APP_CONFIG.storage.userKey, JSON.stringify(userInfo.value))

      // 如果是注册用户，更新本地数据库
      const localUsers = getLocalUserDatabase()
      const userIndex = localUsers.findIndex(u => u.id === userInfo.value.id)
      if (userIndex !== -1) {
        localUsers[userIndex] = { ...localUsers[userIndex], ...updates }
        localStorage.setItem('ai_creative_users_db', JSON.stringify(localUsers))
      }

      ElMessage({
        message: '用户信息更新成功',
        type: 'success',
        duration: 3000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })

      return { success: true }
    } catch (error) {
      console.error('更新用户信息失败:', error)
      ElMessage({
        message: '更新失败，请稍后重试',
        type: 'error',
        duration: 4000,
        showClose: true,
        customClass: 'high-contrast-message',
        center: true
      })
      return { success: false, message: '更新失败' }
    } finally {
      loading.value = false
    }
  }

  // 登出 - 前端模拟
  const logout = async () => {
    try {
      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 清除状态
      token.value = ''
      userInfo.value = null

      // 清除本地存储
      removeToken()
      localStorage.removeItem(APP_CONFIG.storage.userKey)
      localStorage.removeItem(APP_CONFIG.storage.settingsKey)

      ElMessage.success('已退出登录')
    } catch (error) {
      console.error('登出错误:', error)
      // 即使出错，也要清除本地状态
      token.value = ''
      userInfo.value = null
      removeToken()
      localStorage.removeItem(APP_CONFIG.storage.userKey)
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息 - 前端模拟
  const getUserInfo = async () => {
    try {
      if (!token.value) return false

      loading.value = true

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 如果已有用户信息，直接返回
      if (userInfo.value) {
        return true
      }

      // 模拟从token获取用户信息失败（token过期）
      if (Math.random() > 0.9) {
        await logout()
        return false
      }

      return true
    } catch (error) {
      console.error('获取用户信息错误:', error)
      await logout()
      return false
    } finally {
      loading.value = false
    }
  }



  // 更新用户设置
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem(APP_CONFIG.storage.settingsKey, JSON.stringify(settings.value))
  }

  // 初始化用户状态
  const initUserState = async () => {
    try {
      console.log('👤 开始初始化用户状态...')

      // 从本地存储恢复token（安全处理）
      try {
        const savedToken = getToken()
        if (savedToken) {
          token.value = savedToken
          console.log('✅ 恢复token成功')
        }
      } catch (tokenError) {
        console.warn('⚠️ 恢复token失败:', tokenError)
        // 清除可能损坏的token
        removeToken()
      }

      // 从本地存储恢复用户信息（安全处理）
      try {
        const savedUserInfo = localStorage.getItem(APP_CONFIG.storage.userKey)
        if (savedUserInfo) {
          userInfo.value = JSON.parse(savedUserInfo)
          console.log('✅ 恢复用户信息成功:', userInfo.value?.username)
        }
      } catch (userInfoError) {
        console.warn('⚠️ 恢复用户信息失败:', userInfoError)
        // 清除可能损坏的用户信息
        localStorage.removeItem(APP_CONFIG.storage.userKey)
        userInfo.value = null
      }

      // 从本地存储恢复用户设置（安全处理）
      try {
        const savedSettings = localStorage.getItem(APP_CONFIG.storage.settingsKey)
        if (savedSettings) {
          settings.value = { ...DEFAULT_SETTINGS, ...JSON.parse(savedSettings) }
          console.log('✅ 恢复用户设置成功')
        } else {
          settings.value = { ...DEFAULT_SETTINGS }
        }
      } catch (settingsError) {
        console.warn('⚠️ 恢复用户设置失败:', settingsError)
        // 使用默认设置
        localStorage.removeItem(APP_CONFIG.storage.settingsKey)
        settings.value = { ...DEFAULT_SETTINGS }
      }

      console.log('👤 用户登录状态:', isLoggedIn.value)

      // 如果有token但没有用户信息，尝试获取用户信息
      // 暂时注释掉，后端接口未完成
      // if (token.value && !userInfo.value) {
      //   try {
      //     await getUserInfo()
      //   } catch (getUserInfoError) {
      //     console.warn('⚠️ 获取用户信息失败:', getUserInfoError)
      //     // 清除无效的token
      //     logout()
      //   }
      // }

      console.log('✅ 用户状态初始化完成')
    } catch (error) {
      console.error('❌ 用户状态初始化失败:', error)
      // 重置状态以确保应用能正常运行
      try {
        resetState()
        console.log('✅ 用户状态已重置为默认值')
      } catch (resetError) {
        console.error('❌ 重置用户状态也失败了:', resetError)
      }
      // 不再抛出错误，让应用继续运行
      console.warn('⚠️ 用户状态初始化失败，将以游客模式运行')
    }
  }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    settings.value = { ...DEFAULT_SETTINGS }
    loading.value = false
  }

  return {
    // 状态
    token,
    userInfo,
    settings,
    loading,
    
    // 计算属性
    isLoggedIn,
    userName,
    userAvatar,
    userRole,
    isAdmin,
    isVip,
    
    // 方法
    register,
    login,
    logout,
    getUserInfo,
    updateUserInfo,
    updateSettings,
    getRememberMeInfo,
    getLocalUserDatabase,
    checkUserExists,
    initUserState,
    resetState,
  }
})
