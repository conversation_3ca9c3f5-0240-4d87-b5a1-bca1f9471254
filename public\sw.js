// Service Worker for AI Creative Platform
const CACHE_NAME = 'ai-creative-platform-v1.0.0'
const STATIC_CACHE_URLS = [
  '/',
  '/manifest.json',
  '/favicon.ico',
  '/logo.svg',
  '/offline.html',
  '/icons/icon-144x144.svg',
  '/icons/icon-192x192.svg',
  '/icons/icon-512x512.svg'
]

// 安装事件
self.addEventListener('install', (event) => {
  console.log('Service Worker: 安装中...')
  
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: 缓存静态资源')
        return cache.addAll(STATIC_CACHE_URLS)
      })
      .catch((error) => {
        console.error('Service Worker: 缓存静态资源失败', error)
      })
  )
  
  // 强制激活新的Service Worker
  self.skipWaiting()
})

// 激活事件
self.addEventListener('activate', (event) => {
  console.log('Service Worker: 激活中...')
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Service Worker: 删除旧缓存', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  
  // 立即控制所有客户端
  self.clients.claim()
})

// 拦截网络请求
self.addEventListener('fetch', (event) => {
  // 只处理GET请求
  if (event.request.method !== 'GET') {
    return
  }
  
  // 跳过非HTTP(S)请求
  if (!event.request.url.startsWith('http')) {
    return
  }
  
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // 如果缓存中有，直接返回
        if (response) {
          return response
        }
        
        // 否则从网络获取
        return fetch(event.request)
          .then((response) => {
            // 检查响应是否有效
            if (!response || response.status !== 200 || response.type !== 'basic') {
              return response
            }
            
            // 克隆响应，因为响应流只能使用一次
            const responseToCache = response.clone()
            
            // 缓存响应（仅缓存静态资源）
            if (shouldCache(event.request.url)) {
              caches.open(CACHE_NAME)
                .then((cache) => {
                  cache.put(event.request, responseToCache)
                })
            }
            
            return response
          })
          .catch((error) => {
            console.error('Service Worker: 网络请求失败', error)
            
            // 如果是导航请求且网络失败，返回离线页面
            if (event.request.mode === 'navigate') {
              return caches.match('/offline.html')
            }
            
            throw error
          })
      })
  )
})

// 判断是否应该缓存该URL
function shouldCache(url) {
  // 缓存静态资源
  return url.includes('/assets/') || 
         url.includes('/icons/') || 
         url.endsWith('.css') || 
         url.endsWith('.js') || 
         url.endsWith('.svg') || 
         url.endsWith('.png') || 
         url.endsWith('.jpg') || 
         url.endsWith('.jpeg') || 
         url.endsWith('.gif') || 
         url.endsWith('.webp')
}

// 监听消息
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})
