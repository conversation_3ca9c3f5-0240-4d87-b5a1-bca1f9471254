<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <div class="error-icon">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="10" stroke="#ef4444" stroke-width="2"/>
          <line x1="15" y1="9" x2="9" y2="15" stroke="#ef4444" stroke-width="2"/>
          <line x1="9" y1="9" x2="15" y2="15" stroke="#ef4444" stroke-width="2"/>
        </svg>
      </div>
      <h3>组件渲染出错</h3>
      <p>{{ errorMessage }}</p>
      <div class="error-actions">
        <button @click="retry" class="retry-btn">重试</button>
        <button @click="goHome" class="home-btn">返回首页</button>
      </div>
      <details v-if="showDetails" class="error-details">
        <summary>错误详情</summary>
        <pre>{{ errorInfo }}</pre>
      </details>
    </div>
  </div>
  <slot v-else />
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 状态
const hasError = ref(false)
const errorMessage = ref('')
const errorInfo = ref('')
const showDetails = ref(false)

// 捕获子组件错误
onErrorCaptured((error, instance, info) => {
  console.error('ErrorBoundary捕获到错误:', error)
  console.error('错误信息:', info)
  console.error('组件实例:', instance)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  errorInfo.value = `错误: ${error.stack}\n\n组件信息: ${info}`
  
  // 在开发环境显示详情
  if (import.meta.env.DEV) {
    showDetails.value = true
  }
  
  // 阻止错误继续向上传播
  return false
})

// 重试
const retry = () => {
  hasError.value = false
  errorMessage.value = ''
  errorInfo.value = ''
  ElMessage.success('正在重试...')
}

// 返回首页
const goHome = () => {
  router.push('/home')
  ElMessage.info('已返回首页')
}
</script>

<style lang="scss" scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;

  .error-content {
    text-align: center;
    max-width: 500px;

    .error-icon {
      margin-bottom: 1.5rem;
      color: #ef4444;
    }

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1f2937;
      margin-bottom: 1rem;
    }

    p {
      color: #6b7280;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .error-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      margin-bottom: 2rem;

      button {
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &.retry-btn {
          background: #3b82f6;
          color: white;

          &:hover {
            background: #2563eb;
          }
        }

        &.home-btn {
          background: #f3f4f6;
          color: #374151;

          &:hover {
            background: #e5e7eb;
          }
        }
      }
    }

    .error-details {
      text-align: left;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 1rem;

      summary {
        cursor: pointer;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      pre {
        font-size: 0.875rem;
        color: #374151;
        white-space: pre-wrap;
        word-break: break-word;
        margin: 0;
      }
    }
  }
}
</style>
