// 统一导出所有store
export { useAppStore } from './app'
export { useUserStore } from './user'
export { useChatStore } from './chat'
export { useDrawingStore } from './drawing'

// 初始化所有store
export const initStores = async () => {
  console.log('🔧 开始初始化Stores...')

  const results = {
    appStore: null,
    userStore: null,
    chatStore: null,
    drawingStore: null,
    errors: []
  }

  // 初始化应用Store（最重要，必须成功）
  try {
    console.log('📱 初始化应用Store...')
    const appStore = useAppStore()

    // 确保 appStore 存在且有 initApp 方法
    if (appStore && typeof appStore.initApp === 'function') {
      await appStore.initApp()
      results.appStore = appStore
      console.log('✅ 应用Store初始化成功')
    } else {
      console.warn('⚠️ 应用Store或initApp方法不存在')
      results.appStore = appStore
    }
  } catch (error) {
    console.error('❌ 应用Store初始化失败:', error)
    results.errors.push({ store: 'app', error })
    // 应用Store失败不再是致命的，让应用继续运行
    console.warn('⚠️ 应用Store初始化失败，将以默认配置运行')
  }

  // 初始化用户Store（重要，但可以降级）
  try {
    console.log('👤 初始化用户Store...')
    const userStore = useUserStore()

    // 确保 userStore 存在且有 initUserState 方法
    if (userStore && typeof userStore.initUserState === 'function') {
      await userStore.initUserState()
      results.userStore = userStore
      console.log('✅ 用户Store初始化成功')
    } else {
      console.warn('⚠️ 用户Store或initUserState方法不存在')
      results.userStore = userStore
    }
  } catch (error) {
    console.error('❌ 用户Store初始化失败:', error)
    results.errors.push({ store: 'user', error })
    // 用户Store失败不阻塞应用启动
    console.warn('⚠️ 用户Store初始化失败，将以游客模式运行')
  }

  // 初始化聊天Store（可选）
  try {
    console.log('💬 初始化聊天Store...')
    const chatStore = useChatStore()
    await chatStore.loadChatHistory()
    results.chatStore = chatStore
    console.log('✅ 聊天Store初始化成功')
  } catch (error) {
    console.error('❌ 聊天Store初始化失败:', error)
    results.errors.push({ store: 'chat', error })
    console.warn('⚠️ 聊天Store初始化失败，聊天功能可能受限')
  }

  // 初始化绘画Store（可选）
  try {
    console.log('🎨 初始化绘画Store...')
    const drawingStore = useDrawingStore()
    await drawingStore.loadDrawingHistory()
    results.drawingStore = drawingStore
    console.log('✅ 绘画Store初始化成功')
  } catch (error) {
    console.error('❌ 绘画Store初始化失败:', error)
    results.errors.push({ store: 'drawing', error })
    console.warn('⚠️ 绘画Store初始化失败，绘画功能可能受限')
  }

  const successCount = Object.values(results).filter(v => v !== null && !Array.isArray(v)).length
  const totalStores = 4

  if (results.errors.length > 0) {
    console.warn(`⚠️ Stores初始化完成，但有 ${results.errors.length} 个Store初始化失败`)
    console.warn('失败详情:', results.errors)
  }

  console.log(`✅ Stores初始化完成 (${successCount}/${totalStores} 成功)`)

  return results
}

// 重置所有store
export const resetAllStores = () => {
  const appStore = useAppStore()
  const userStore = useUserStore()
  const chatStore = useChatStore()
  const drawingStore = useDrawingStore()

  appStore.resetAppState()
  userStore.resetState()
  chatStore.resetState()
  drawingStore.resetState()
}
