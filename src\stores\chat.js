import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { APP_CONFIG, CONSTANTS } from '@/config'
// import { sendMessageApi, getChatHistoryApi, deleteChatApi } from '@/api/chat'
import { generateId } from '@/utils/common'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const conversations = ref([])
  const currentConversationId = ref(null)
  const messages = ref([])
  const loading = ref(false)
  const typing = ref(false)
  const settings = ref({
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 4000,
    systemPrompt: '你是一个有用的AI助手，请用中文回答问题。',
  })

  // 计算属性
  const currentConversation = computed(() => {
    return conversations.value.find(conv => conv.id === currentConversationId.value)
  })

  const currentMessages = computed(() => {
    return messages.value.filter(msg => msg.conversationId === currentConversationId.value)
  })

  const hasMessages = computed(() => currentMessages.value.length > 0)

  const lastMessage = computed(() => {
    const msgs = currentMessages.value
    return msgs.length > 0 ? msgs[msgs.length - 1] : null
  })

  // 创建新对话
  const createConversation = (title = '新对话') => {
    const conversation = {
      id: generateId(),
      title,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      messageCount: 0,
    }
    
    conversations.value.unshift(conversation)
    currentConversationId.value = conversation.id
    
    // 保存到本地存储
    saveChatHistory()
    
    return conversation
  }

  // 切换对话
  const switchConversation = (conversationId) => {
    currentConversationId.value = conversationId
  }

  // 删除对话 - 前端模拟
  const deleteConversation = async (conversationId) => {
    try {
      // 如果是当前对话，先切换到其他对话或创建新对话
      if (conversationId === currentConversationId.value) {
        const otherConversations = conversations.value.filter(conv => conv.id !== conversationId)
        if (otherConversations.length > 0) {
          currentConversationId.value = otherConversations[0].id
        } else {
          currentConversationId.value = null
        }
      }

      // 删除对话和相关消息
      conversations.value = conversations.value.filter(conv => conv.id !== conversationId)
      messages.value = messages.value.filter(msg => msg.conversationId !== conversationId)

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 300))

      // 保存到本地存储
      saveChatHistory()

      ElMessage.success('对话已删除')
    } catch (error) {
      console.error('删除对话错误:', error)
      ElMessage.error('删除对话失败')
    }
  }

  // 发送消息
  const sendMessage = async (content, type = CONSTANTS.MESSAGE_TYPES.TEXT) => {
    if (!content.trim()) return

    // 如果没有当前对话，创建一个
    if (!currentConversationId.value) {
      createConversation()
    }

    // 创建用户消息
    const userMessage = {
      id: generateId(),
      conversationId: currentConversationId.value,
      content: content.trim(),
      type,
      role: 'user',
      timestamp: new Date().toISOString(),
      status: CONSTANTS.CHAT_STATUS.COMPLETED,
    }

    // 添加用户消息
    messages.value.push(userMessage)

    // 创建AI消息占位符
    const aiMessage = {
      id: generateId(),
      conversationId: currentConversationId.value,
      content: '',
      type: CONSTANTS.MESSAGE_TYPES.TEXT,
      role: 'assistant',
      timestamp: new Date().toISOString(),
      status: CONSTANTS.CHAT_STATUS.WAITING,
    }

    messages.value.push(aiMessage)

    try {
      loading.value = true
      typing.value = true
      aiMessage.status = CONSTANTS.CHAT_STATUS.TYPING

      // 准备发送的消息历史
      const messageHistory = currentMessages.value
        .filter(msg => msg.status === CONSTANTS.CHAT_STATUS.COMPLETED)
        .map(msg => ({
          role: msg.role,
          content: msg.content,
        }))

      // 模拟AI回复 - 前端模拟
      const mockResponses = [
        '这是一个很有趣的问题！让我来为您详细解答...',
        '根据您的描述，我建议您可以尝试以下几种方法：\n1. 首先...\n2. 其次...\n3. 最后...',
        '我理解您的需求。这个问题确实需要仔细考虑。',
        '很高兴为您提供帮助！基于我的知识，我认为...',
        '这是一个常见的问题，许多人都会遇到。让我为您分析一下...',
        '感谢您的提问！我会尽力为您提供准确和有用的信息。',
        '您提到的这个话题很有价值，让我们一起探讨一下...',
        '根据最新的信息和研究，我可以告诉您...'
      ]

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))

      // 随机选择一个回复
      const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)]

      // 更新AI消息
      aiMessage.content = randomResponse
      aiMessage.status = CONSTANTS.CHAT_STATUS.COMPLETED
      aiMessage.timestamp = new Date().toISOString()

      // 更新对话信息
      const conversation = conversations.value.find(conv => conv.id === currentConversationId.value)
      if (conversation) {
        conversation.updatedAt = new Date().toISOString()
        conversation.messageCount = currentMessages.value.length

        // 如果是第一条消息，更新对话标题
        if (conversation.messageCount === 2 && conversation.title === '新对话') {
          conversation.title = content.slice(0, 20) + (content.length > 20 ? '...' : '')
        }
      }

      // 保存到本地存储
      saveChatHistory()
    } catch (error) {
      console.error('发送消息错误:', error)
      aiMessage.content = '网络连接错误，请检查网络后重试。'
      aiMessage.status = CONSTANTS.CHAT_STATUS.ERROR
      ElMessage.error('发送消息失败')
    } finally {
      loading.value = false
      typing.value = false
    }
  }

  // 重新发送消息
  const resendMessage = async (messageId) => {
    const message = messages.value.find(msg => msg.id === messageId)
    if (!message || message.role !== 'user') return

    // 删除该消息之后的所有消息
    const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
    messages.value = messages.value.slice(0, messageIndex)

    // 重新发送
    await sendMessage(message.content, message.type)
  }

  // 删除消息
  const deleteMessage = (messageId) => {
    messages.value = messages.value.filter(msg => msg.id !== messageId)
    saveChatHistory()
  }

  // 更新聊天设置
  const updateSettings = (newSettings) => {
    settings.value = { ...settings.value, ...newSettings }
    localStorage.setItem('chat_settings', JSON.stringify(settings.value))
  }

  // 保存聊天历史到本地存储
  const saveChatHistory = () => {
    const chatData = {
      conversations: conversations.value,
      messages: messages.value,
      currentConversationId: currentConversationId.value,
    }
    localStorage.setItem(APP_CONFIG.storage.chatHistoryKey, JSON.stringify(chatData))
  }

  // 从本地存储加载聊天历史
  const loadChatHistory = async () => {
    try {
      console.log('💬 开始加载聊天历史...')

      // 安全加载聊天历史数据
      try {
        const savedData = localStorage.getItem(APP_CONFIG.storage.chatHistoryKey)
        if (savedData) {
          const chatData = JSON.parse(savedData)
          conversations.value = Array.isArray(chatData.conversations) ? chatData.conversations : []
          messages.value = Array.isArray(chatData.messages) ? chatData.messages : []
          currentConversationId.value = chatData.currentConversationId || null
          console.log(`✅ 聊天历史加载完成: ${conversations.value.length}个对话, ${messages.value.length}条消息`)
        } else {
          console.log('📝 没有找到聊天历史数据')
        }
      } catch (historyError) {
        console.warn('⚠️ 聊天历史数据损坏，已重置:', historyError)
        localStorage.removeItem(APP_CONFIG.storage.chatHistoryKey)
        conversations.value = []
        messages.value = []
        currentConversationId.value = null
      }

      // 安全加载聊天设置
      try {
        const savedSettings = localStorage.getItem('chat_settings')
        if (savedSettings) {
          const parsedSettings = JSON.parse(savedSettings)
          settings.value = { ...settings.value, ...parsedSettings }
          console.log('✅ 聊天设置加载完成')
        } else {
          console.log('⚙️ 使用默认聊天设置')
        }
      } catch (settingsError) {
        console.warn('⚠️ 聊天设置数据损坏，使用默认设置:', settingsError)
        localStorage.removeItem('chat_settings')
      }
    } catch (error) {
      console.error('❌ 加载聊天历史失败:', error)
      // 重置为默认状态，但不抛出错误
      conversations.value = []
      messages.value = []
      currentConversationId.value = null
      console.warn('⚠️ 聊天历史加载失败，已重置为空状态')
    }
  }

  // 清空聊天历史
  const clearChatHistory = () => {
    conversations.value = []
    messages.value = []
    currentConversationId.value = null
    localStorage.removeItem(APP_CONFIG.storage.chatHistoryKey)
    ElMessage.success('聊天历史已清空')
  }

  // 重置状态
  const resetState = () => {
    conversations.value = []
    messages.value = []
    currentConversationId.value = null
    loading.value = false
    typing.value = false
    settings.value = {
      model: 'gpt-3.5-turbo',
      temperature: 0.7,
      maxTokens: 4000,
      systemPrompt: '你是一个有用的AI助手，请用中文回答问题。',
    }
  }

  return {
    // 状态
    conversations,
    currentConversationId,
    messages,
    loading,
    typing,
    settings,
    
    // 计算属性
    currentConversation,
    currentMessages,
    hasMessages,
    lastMessage,
    
    // 方法
    createConversation,
    switchConversation,
    deleteConversation,
    sendMessage,
    resendMessage,
    deleteMessage,
    updateSettings,
    saveChatHistory,
    loadChatHistory,
    clearChatHistory,
    resetState,
  }
})
