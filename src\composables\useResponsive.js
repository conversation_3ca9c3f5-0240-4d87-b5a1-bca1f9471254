// 响应式设计组合式函数
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 断点配置
const breakpoints = {
  xs: 480,
  sm: 768,
  md: 992,
  lg: 1200,
  xl: 1920,
}

export function useResponsive() {
  const windowWidth = ref(typeof window !== 'undefined' ? window.innerWidth : 1024)
  const windowHeight = ref(typeof window !== 'undefined' ? window.innerHeight : 768)

  // 更新窗口尺寸
  const updateSize = () => {
    if (typeof window !== 'undefined') {
      windowWidth.value = window.innerWidth
      windowHeight.value = window.innerHeight
    }
  }

  // 计算属性
  const screenSize = computed(() => ({
    width: windowWidth.value,
    height: windowHeight.value,
  }))

  const isMobile = computed(() => windowWidth.value < breakpoints.sm)
  const isTablet = computed(() =>
    windowWidth.value >= breakpoints.sm && windowWidth.value < breakpoints.lg
  )
  const isDesktop = computed(() => windowWidth.value >= breakpoints.lg)
  const isLargeScreen = computed(() => windowWidth.value >= breakpoints.xl)

  // 新增更细粒度的断点判断
  const isExtraSmall = computed(() => windowWidth.value < breakpoints.xs)
  const isSmallMobile = computed(() => windowWidth.value < 400)
  const isTouchDevice = computed(() => typeof window !== 'undefined' && 'ontouchstart' in window)

  // 设备方向
  const isLandscape = computed(() => windowWidth.value > windowHeight.value)
  const isPortrait = computed(() => windowWidth.value <= windowHeight.value)

  // 屏幕密度
  const pixelRatio = computed(() => typeof window !== 'undefined' ? (window.devicePixelRatio || 1) : 1)
  const isHighDPI = computed(() => pixelRatio.value > 1.5)

  const currentBreakpoint = computed(() => {
    const width = windowWidth.value
    if (width < breakpoints.xs) return 'xs'
    if (width < breakpoints.sm) return 'sm'
    if (width < breakpoints.md) return 'md'
    if (width < breakpoints.lg) return 'lg'
    return 'xl'
  })

  // 断点检查函数
  const isBreakpoint = (bp) => {
    return currentBreakpoint.value === bp
  }

  const isBreakpointUp = (bp) => {
    const bpValue = breakpoints[bp]
    return windowWidth.value >= bpValue
  }

  const isBreakpointDown = (bp) => {
    const bpValue = breakpoints[bp]
    return windowWidth.value < bpValue
  }

  const isBreakpointBetween = (minBp, maxBp) => {
    const minValue = breakpoints[minBp]
    const maxValue = breakpoints[maxBp]
    return windowWidth.value >= minValue && windowWidth.value < maxValue
  }

  // 生命周期
  onMounted(() => {
    window.addEventListener('resize', updateSize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateSize)
  })

  return {
    // 响应式数据
    windowWidth,
    windowHeight,
    screenSize,

    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    isLargeScreen,
    isExtraSmall,
    isSmallMobile,
    isTouchDevice,
    currentBreakpoint,

    // 设备方向
    isLandscape,
    isPortrait,

    // 屏幕密度
    pixelRatio,
    isHighDPI,

    // 方法
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    isBreakpointBetween,

    // 断点常量
    breakpoints,
  }
}

// 设备检测
export function useDeviceDetection() {
  const userAgent = navigator.userAgent.toLowerCase()
  
  const isIOS = computed(() => {
    return /iphone|ipad|ipod/.test(userAgent)
  })
  
  const isAndroid = computed(() => {
    return /android/.test(userAgent)
  })
  
  const isMobileDevice = computed(() => {
    return isIOS.value || isAndroid.value || /mobile/.test(userAgent)
  })
  
  const isTabletDevice = computed(() => {
    return /ipad/.test(userAgent) || 
           (/android/.test(userAgent) && !/mobile/.test(userAgent))
  })
  
  const isTouchDevice = computed(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  })
  
  const browserInfo = computed(() => {
    let browser = 'Unknown'
    
    if (userAgent.includes('chrome')) browser = 'Chrome'
    else if (userAgent.includes('firefox')) browser = 'Firefox'
    else if (userAgent.includes('safari')) browser = 'Safari'
    else if (userAgent.includes('edge')) browser = 'Edge'
    else if (userAgent.includes('opera')) browser = 'Opera'
    
    return {
      name: browser,
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
    }
  })
  
  return {
    isIOS,
    isAndroid,
    isMobileDevice,
    isTabletDevice,
    isTouchDevice,
    browserInfo,
  }
}

// 媒体查询
export function useMediaQuery(query) {
  const matches = ref(false)
  let mediaQuery = null
  
  const updateMatches = (e) => {
    matches.value = e.matches
  }
  
  onMounted(() => {
    mediaQuery = window.matchMedia(query)
    matches.value = mediaQuery.matches
    mediaQuery.addEventListener('change', updateMatches)
  })
  
  onUnmounted(() => {
    if (mediaQuery) {
      mediaQuery.removeEventListener('change', updateMatches)
    }
  })
  
  return matches
}

// 方向检测
export function useOrientation() {
  const orientation = ref(screen.orientation?.type || 'portrait-primary')
  const isPortrait = computed(() => orientation.value.includes('portrait'))
  const isLandscape = computed(() => orientation.value.includes('landscape'))
  
  const updateOrientation = () => {
    orientation.value = screen.orientation?.type || 
      (window.innerHeight > window.innerWidth ? 'portrait-primary' : 'landscape-primary')
  }
  
  onMounted(() => {
    updateOrientation()
    window.addEventListener('orientationchange', updateOrientation)
    window.addEventListener('resize', updateOrientation)
  })
  
  onUnmounted(() => {
    window.removeEventListener('orientationchange', updateOrientation)
    window.removeEventListener('resize', updateOrientation)
  })
  
  return {
    orientation,
    isPortrait,
    isLandscape,
  }
}

// 网络状态检测
export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const connectionType = ref(navigator.connection?.effectiveType || 'unknown')
  const downlink = ref(navigator.connection?.downlink || 0)
  
  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine
    if (navigator.connection) {
      connectionType.value = navigator.connection.effectiveType
      downlink.value = navigator.connection.downlink
    }
  }
  
  onMounted(() => {
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)
    
    if (navigator.connection) {
      navigator.connection.addEventListener('change', updateNetworkStatus)
    }
  })
  
  onUnmounted(() => {
    window.removeEventListener('online', updateNetworkStatus)
    window.removeEventListener('offline', updateNetworkStatus)
    
    if (navigator.connection) {
      navigator.connection.removeEventListener('change', updateNetworkStatus)
    }
  })
  
  return {
    isOnline,
    connectionType,
    downlink,
  }
}

// 暗色模式检测
export function useDarkMode() {
  const isDarkMode = useMediaQuery('(prefers-color-scheme: dark)')
  
  return {
    isDarkMode,
  }
}

// 减少动画偏好检测
export function useReducedMotion() {
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)')
  
  return {
    prefersReducedMotion,
  }
}
