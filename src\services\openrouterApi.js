// OpenRouter AI API 服务
import { ElMessage } from 'element-plus'
import { apiKeyManager } from './apiKeyManager.js'
import { shouldUseMockData, getMockApiKeyManager } from '@/config/devConfig.js'

// API 基础配置
const API_CONFIG = {
  baseUrl: import.meta.env.VITE_OPENROUTER_BASE_URL || 'https://openrouter.ai/api/v1',
  timeout: parseInt(import.meta.env.VITE_OPENROUTER_TIMEOUT) || 60000,
  maxRetries: parseInt(import.meta.env.VITE_OPENROUTER_MAX_RETRIES) || 3,
  retryDelay: parseInt(import.meta.env.VITE_OPENROUTER_RETRY_DELAY) || 1000,
  defaultModel: 'deepseek/deepseek-chat-v3-0324:free'
}

// 支持的免费模型列表
export const OPENROUTER_FREE_MODELS = [
  'deepseek/deepseek-chat-v3-0324:free',
  'deepseek/deepseek-r1-0528:free',
  'qwen/qwen3-coder:free',
  'deepseek/deepseek-r1:free',
  'qwen/qwen3-235b-a22b-2507:free',
  'tngtech/deepseek-r1t2-chimera:free',
  'moonshotai/kimi-k2:free',
  'tngtech/deepseek-r1t-chimera:free',
  'google/gemini-2.0-flash-exp:free',
  'qwen/qwq-32b:free',
  'qwen/qwen3-14b:free',
  'mistralai/mistral-nemo:free',
  'deepseek/deepseek-r1-0528-qwen3-8b:free',
  'microsoft/mai-ds-r1:free',
  'qwen/qwen3-235b-a22b:free',
  'mistralai/mistral-small-3.1-24b-instruct:free',
  'qwen/qwen2.5-vl-72b-instruct:free',
  'mistralai/mistral-small-3.2-24b-instruct:free',
  'moonshotai/kimi-dev-72b:free',
  'google/gemma-3-27b-it:free',
  'qwen/qwen3-30b-a3b:free',
  'qwen/qwen-2.5-coder-32b-instruct:free',
  'mistralai/devstral-small-2505:free',
  'mistralai/mistral-7b-instruct:free',
  'agentica-org/deepcoder-14b-preview:free',
  'deepseek/deepseek-r1-distill-llama-70b:free',
  'meta-llama/llama-3.3-70b-instruct:free',
  'qwen/qwen3-8b:free',
  'meta-llama/llama-3.2-11b-vision-instruct:free',
  'cognitivecomputations/dolphin-mistral-24b-venice-edition:free',
  'moonshotai/kimi-vl-a3b-thinking:free',
  'qwen/qwen-2.5-72b-instruct:free',
  'thudm/glm-z1-32b:free',
  'shisa-ai/shisa-v2-llama3.3-70b:free',
  'cognitivecomputations/dolphin3.0-mistral-24b:free',
  'qwen/qwen2.5-vl-32b-instruct:free',
  'tencent/hunyuan-a13b-instruct:free',
  'nousresearch/deephermes-3-llama-3-8b-preview:free',
  'google/gemma-3n-e2b-it:free',
  'thudm/glm-4-32b:free',
  'google/gemma-2-9b-it:free',
  'google/gemma-3-12b-it:free',
  'meta-llama/llama-3.1-405b-instruct:free',
  'cognitivecomputations/dolphin3.0-r1-mistral-24b:free',
  'mistralai/mistral-small-24b-instruct-2501:free',
  'arliai/qwq-32b-arliai-rpr-v1:free',
  'google/gemma-3n-e4b-it:free',
  'google/gemma-3-4b-it:free',
  'rekaai/reka-flash-3:free',
  'sarvamai/sarvam-m:free',
  'qwen/qwen3-4b:free',
  'featherless/qwerky-72b:free',
  'meta-llama/llama-3.2-3b-instruct:free',
  'nvidia/llama-3.1-nemotron-ultra-253b-v1:free',
  'deepseek/deepseek-r1-distill-qwen-14b:free'
]

// 自定义错误类
class OpenRouterError extends Error {
  constructor(message, status = 500, details = null) {
    super(message)
    this.name = 'OpenRouterError'
    this.status = status
    this.details = details
  }
}

// HTTP 请求工具 - 支持密钥轮询和重试
async function makeRequest(url, options = {}) {
  let lastError = null
  const startTime = Date.now()

  for (let attempt = 0; attempt < API_CONFIG.maxRetries; attempt++) {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), API_CONFIG.timeout)

    try {
      // 获取当前可用的API密钥
      const apiKey = apiKeyManager.getCurrentKey()

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'AI Creative Platform',
          ...options.headers
        }
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        let errorData
        try {
          errorData = JSON.parse(errorText)
        } catch {
          errorData = { message: errorText }
        }

        const error = new OpenRouterError(
          errorData.error?.message || `API请求失败: ${response.status} ${response.statusText}`,
          response.status,
          errorData
        )

        // 根据错误类型决定是否重试
        if (shouldRetry(response.status, attempt)) {
          apiKeyManager.markCurrentKeyFailed(error)
          lastError = error

          // 如果是认证错误，立即轮换密钥
          if (response.status === 401 || response.status === 403) {
            apiKeyManager.rotateKey()
          }

          // 等待后重试
          if (attempt < API_CONFIG.maxRetries - 1) {
            await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay * (attempt + 1)))
            continue
          }
        }

        throw error
      }

      // 请求成功，记录统计信息
      const responseTime = Date.now() - startTime
      apiKeyManager.markCurrentKeySuccess(responseTime)

      return response

    } catch (error) {
      clearTimeout(timeoutId)

      if (error.name === 'AbortError') {
        lastError = new OpenRouterError('请求超时，请稍后重试', 408)
      } else if (error instanceof OpenRouterError) {
        lastError = error
      } else {
        lastError = new OpenRouterError(error.message || '网络请求失败', 500)
      }

      // 标记当前密钥失败
      apiKeyManager.markCurrentKeyFailed(lastError)

      // 如果还有重试机会，继续重试
      if (attempt < API_CONFIG.maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay * (attempt + 1)))
        continue
      }
    }
  }

  // 所有重试都失败了
  throw lastError || new OpenRouterError('请求失败', 500)
}

// 判断是否应该重试
function shouldRetry(statusCode, attempt) {
  // 超过最大重试次数
  if (attempt >= API_CONFIG.maxRetries - 1) {
    return false
  }

  // 这些状态码值得重试
  const retryableStatusCodes = [
    401, // Unauthorized - 可能是密钥问题，尝试其他密钥
    403, // Forbidden - 可能是密钥问题
    429, // Too Many Requests - 速率限制
    500, // Internal Server Error
    502, // Bad Gateway
    503, // Service Unavailable
    504  // Gateway Timeout
  ]

  return retryableStatusCodes.includes(statusCode)
}

// 获取可用模型列表
export async function getAvailableModels() {
  try {
    const response = await makeRequest(`${API_CONFIG.baseUrl}/models`)
    const data = await response.json()
    
    // 过滤出免费模型
    const freeModels = data.data.filter(model => 
      OPENROUTER_FREE_MODELS.includes(model.id)
    )
    
    return freeModels.map(model => ({
      id: model.id,
      name: model.name || model.id,
      description: model.description || '',
      context_length: model.context_length || 4096,
      pricing: model.pricing || {},
      top_provider: model.top_provider || {}
    }))
  } catch (error) {
    console.error('获取OpenRouter模型列表失败:', error)
    throw error
  }
}

// 生成聊天完成
export async function generateChatCompletion(messages, options = {}) {
  try {
    const requestBody = {
      model: options.model || API_CONFIG.defaultModel,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 2048,
      top_p: options.top_p || 1,
      frequency_penalty: options.frequency_penalty || 0,
      presence_penalty: options.presence_penalty || 0,
      stream: false
    }

    const response = await makeRequest(`${API_CONFIG.baseUrl}/chat/completions`, {
      method: 'POST',
      body: JSON.stringify(requestBody)
    })

    const data = await response.json()
    
    if (!data.choices || data.choices.length === 0) {
      throw new OpenRouterError('API返回了空的响应')
    }

    return {
      content: data.choices[0].message.content,
      model: data.model,
      usage: data.usage,
      finish_reason: data.choices[0].finish_reason
    }
  } catch (error) {
    console.error('OpenRouter聊天完成失败:', error)
    throw error
  }
}

// 流式生成聊天完成
export async function generateChatCompletionStream(messages, options = {}, onChunk) {
  try {
    const requestBody = {
      model: options.model || API_CONFIG.defaultModel,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 2048,
      top_p: options.top_p || 1,
      frequency_penalty: options.frequency_penalty || 0,
      presence_penalty: options.presence_penalty || 0,
      stream: true
    }

    const response = await makeRequest(`${API_CONFIG.baseUrl}/chat/completions`, {
      method: 'POST',
      body: JSON.stringify(requestBody)
    })

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          const trimmed = line.trim()
          if (trimmed === '' || trimmed === 'data: [DONE]') continue
          
          if (trimmed.startsWith('data: ')) {
            try {
              const jsonStr = trimmed.slice(6)
              const data = JSON.parse(jsonStr)
              
              if (data.choices && data.choices[0] && data.choices[0].delta) {
                const content = data.choices[0].delta.content
                if (content) {
                  onChunk(content)
                }
              }
            } catch (parseError) {
              console.warn('解析流数据失败:', parseError)
            }
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  } catch (error) {
    console.error('OpenRouter流式聊天失败:', error)
    throw error
  }
}

// 获取模型信息
export function getModelInfo(modelId) {
  // 解析模型ID获取提供商和模型名称
  const parts = modelId.split('/')
  const provider = parts[0]
  const modelName = parts[1]?.replace(':free', '') || modelId
  
  const providerMap = {
    'deepseek': 'DeepSeek',
    'qwen': 'Alibaba',
    'moonshotai': 'Moonshot AI',
    'google': 'Google',
    'mistralai': 'Mistral AI',
    'microsoft': 'Microsoft',
    'meta-llama': 'Meta',
    'tngtech': 'TNG Technology',
    'agentica-org': 'Agentica',
    'cognitivecomputations': 'Cognitive Computations',
    'thudm': 'Tsinghua University',
    'shisa-ai': 'Shisa AI',
    'tencent': 'Tencent',
    'nousresearch': 'Nous Research',
    'arliai': 'Arli AI',
    'rekaai': 'Reka AI',
    'sarvamai': 'Sarvam AI',
    'featherless': 'Featherless',
    'nvidia': 'NVIDIA'
  }

  return {
    name: modelName,
    provider: providerMap[provider] || provider,
    capabilities: ['text'],
    isFree: true
  }
}

// 错误处理
export function handleOpenRouterError(error) {
  if (error instanceof OpenRouterError) {
    switch (error.status) {
      case 401:
        ElMessage.error('API密钥无效，请检查配置')
        break
      case 402:
        ElMessage.error('余额不足，请充值后重试')
        break
      case 429:
        ElMessage.error('请求过于频繁，请稍后重试')
        break
      case 500:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
      default:
        ElMessage.error(error.message || '请求失败')
    }
  } else {
    ElMessage.error('网络错误，请检查网络连接')
  }
  console.error('OpenRouter API错误:', error)
}

// API密钥管理相关函数
export function getApiKeyStats() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().getKeyStats()
  }
  return apiKeyManager.getKeyStats()
}

export function getApiConfig() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().getConfig()
  }
  return apiKeyManager.getConfig()
}

export function addApiKey(apiKey) {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().addKey(apiKey)
  }
  return apiKeyManager.addKey(apiKey)
}

export function removeApiKey(index) {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().removeKey(index)
  }
  return apiKeyManager.removeKey(index)
}

export function rotateApiKey() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().rotateKey()
  }
  return apiKeyManager.rotateKey()
}

export function performHealthCheck() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().performHealthCheck()
  }
  return apiKeyManager.performHealthCheck()
}

export function resetAllApiKeys() {
  if (shouldUseMockData()) {
    return getMockApiKeyManager().resetAllKeys()
  }
  return apiKeyManager.resetAllKeys()
}

// 增强的管理功能
export function testApiKey(index) {
  if (shouldUseMockData()) {
    // 模拟测试
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: Math.random() > 0.2, // 80% 成功率
          responseTime: Math.floor(Math.random() * 500) + 100,
          message: '测试完成'
        })
      }, 1000)
    })
  }

  // 实际测试逻辑
  return apiKeyManager.testKey(index)
}

export function switchToApiKey(index) {
  if (shouldUseMockData()) {
    const manager = getMockApiKeyManager()
    manager.currentKeyIndex = index
    manager.keyStats.forEach((key, i) => {
      key.isCurrent = i === index
    })
    return
  }

  return apiKeyManager.switchToKey(index)
}

export function getApiKeyDetails(index) {
  if (shouldUseMockData()) {
    const manager = getMockApiKeyManager()
    const keyStats = manager.keyStats[index]
    if (!keyStats) return null

    return {
      ...keyStats,
      createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      lastHealthCheck: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
      totalDataTransferred: Math.floor(Math.random() * 1000000),
      averageTokensPerRequest: Math.floor(Math.random() * 1000) + 100
    }
  }

  return apiKeyManager.getKeyDetails(index)
}

export function updateApiKeyConfig(config) {
  if (shouldUseMockData()) {
    console.log('模拟更新API配置:', config)
    return Promise.resolve()
  }

  return apiKeyManager.updateConfig(config)
}

export function exportApiKeyStats() {
  const stats = getApiKeyStats()
  const config = getApiConfig()

  const exportData = {
    exportTime: new Date().toISOString(),
    systemConfig: config,
    keyStats: stats,
    summary: {
      totalKeys: stats.length,
      activeKeys: stats.filter(key => key.isActive).length,
      totalRequests: stats.reduce((sum, key) => sum + (key.totalRequests || 0), 0),
      totalSuccessfulRequests: stats.reduce((sum, key) => sum + (key.successfulRequests || 0), 0)
    }
  }

  return exportData
}

export default {
  getAvailableModels,
  generateChatCompletion,
  generateChatCompletionStream,
  getModelInfo,
  handleOpenRouterError,
  getApiKeyStats,
  getApiConfig,
  addApiKey,
  removeApiKey,
  rotateApiKey,
  performHealthCheck,
  resetAllApiKeys,
  OPENROUTER_FREE_MODELS
}
