<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="AI创作助手平台 - 集AI聊天、AI绘画、内容创作和社交分享于一体的综合性创作平台" />
    <meta name="keywords" content="AI创作,AI聊天,AI绘画,内容创作,社交分享" />
    <title>AI创作助手平台</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>

    <!-- Service Worker 注册 -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('Service Worker 注册成功:', registration.scope)
            })
            .catch((error) => {
              console.log('Service Worker 注册失败:', error)
            })
        })
      }
    </script>
  </body>
</html>
