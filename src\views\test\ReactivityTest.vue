<template>
  <div class="reactivity-test">
    <h1>响应式对象测试</h1>
    
    <div class="test-section">
      <h2>应用Store测试</h2>
      <div class="test-item">
        <label>设备类型:</label>
        <span>{{ safeDevice }}</span>
      </div>
      <div class="test-item">
        <label>屏幕尺寸:</label>
        <span>{{ safeScreenSize }}</span>
      </div>
      <div class="test-item">
        <label>是否移动端:</label>
        <span>{{ safeIsMobile }}</span>
      </div>
    </div>

    <div class="test-section">
      <h2>用户Store测试</h2>
      <div class="test-item">
        <label>登录状态:</label>
        <span>{{ safeIsLoggedIn }}</span>
      </div>
      <div class="test-item">
        <label>用户名:</label>
        <span>{{ safeUserName }}</span>
      </div>
      <div class="test-item">
        <label>用户角色:</label>
        <span>{{ safeUserRole }}</span>
      </div>
      <div class="test-item">
        <label>是否管理员:</label>
        <span>{{ safeIsAdmin }}</span>
      </div>
    </div>

    <div class="test-section">
      <h2>操作测试</h2>
      <button @click="testAppStore" class="test-btn">测试应用Store</button>
      <button @click="testUserStore" class="test-btn">测试用户Store</button>
      <button @click="refreshStores" class="test-btn">刷新Stores</button>
    </div>

    <div class="test-section">
      <h2>错误日志</h2>
      <div class="error-log">
        <div v-for="(error, index) in errorLog" :key="index" class="error-item">
          {{ error }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppStore, useUserStore } from '@/stores'

const errorLog = ref([])

// 安全地获取stores
const appStore = useAppStore()
const userStore = useUserStore()

// 安全的计算属性
const safeDevice = computed(() => {
  try {
    return appStore?.device || '未知'
  } catch (error) {
    errorLog.value.push(`获取设备类型错误: ${error.message}`)
    return '错误'
  }
})

const safeScreenSize = computed(() => {
  try {
    const size = appStore?.screenSize
    return size ? `${size.width}x${size.height}` : '未知'
  } catch (error) {
    errorLog.value.push(`获取屏幕尺寸错误: ${error.message}`)
    return '错误'
  }
})

const safeIsMobile = computed(() => {
  try {
    return appStore?.isMobile || false
  } catch (error) {
    errorLog.value.push(`获取移动端状态错误: ${error.message}`)
    return false
  }
})

const safeIsLoggedIn = computed(() => {
  try {
    return userStore?.isLoggedIn || false
  } catch (error) {
    errorLog.value.push(`获取登录状态错误: ${error.message}`)
    return false
  }
})

const safeUserName = computed(() => {
  try {
    return userStore?.userName || '游客'
  } catch (error) {
    errorLog.value.push(`获取用户名错误: ${error.message}`)
    return '错误'
  }
})

const safeUserRole = computed(() => {
  try {
    return userStore?.userRole || 'user'
  } catch (error) {
    errorLog.value.push(`获取用户角色错误: ${error.message}`)
    return '错误'
  }
})

const safeIsAdmin = computed(() => {
  try {
    return userStore?.isAdmin || false
  } catch (error) {
    errorLog.value.push(`获取管理员状态错误: ${error.message}`)
    return false
  }
})

// 测试函数
const testAppStore = () => {
  try {
    console.log('测试应用Store:', {
      device: appStore.device,
      screenSize: appStore.screenSize,
      isMobile: appStore.isMobile,
      theme: appStore.theme
    })
    errorLog.value.push('应用Store测试成功')
  } catch (error) {
    errorLog.value.push(`应用Store测试失败: ${error.message}`)
  }
}

const testUserStore = () => {
  try {
    console.log('测试用户Store:', {
      isLoggedIn: userStore.isLoggedIn,
      userName: userStore.userName,
      userRole: userStore.userRole,
      isAdmin: userStore.isAdmin
    })
    errorLog.value.push('用户Store测试成功')
  } catch (error) {
    errorLog.value.push(`用户Store测试失败: ${error.message}`)
  }
}

const refreshStores = async () => {
  try {
    if (appStore.initApp) {
      await appStore.initApp()
    }
    if (userStore.initUserState) {
      await userStore.initUserState()
    }
    errorLog.value.push('Stores刷新成功')
  } catch (error) {
    errorLog.value.push(`Stores刷新失败: ${error.message}`)
  }
}

onMounted(() => {
  errorLog.value.push('组件已挂载')
})
</script>

<style scoped>
.reactivity-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.test-item label {
  font-weight: bold;
}

.test-btn {
  margin-right: 10px;
  padding: 8px 16px;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:hover {
  background: #0056CC;
}

.error-log {
  max-height: 200px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.error-item {
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
}
</style>
