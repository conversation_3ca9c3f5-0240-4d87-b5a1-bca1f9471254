/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AdminGuard: typeof import('./src/components/admin/AdminGuard.vue')['default']
    AdminNavigation: typeof import('./src/components/admin/AdminNavigation.vue')['default']
    AppLoader: typeof import('./src/components/common/AppLoader.vue')['default']
    AppSidebar: typeof import('./src/components/common/AppSidebar.vue')['default']
    ArtworkDetail: typeof import('./src/components/gallery/ArtworkDetail.vue')['default']
    ChatHeader: typeof import('./src/components/chat/ChatHeader.vue')['default']
    ChatInput: typeof import('./src/components/chat/ChatInput.vue')['default']
    ChatSidebar: typeof import('./src/components/chat/ChatSidebar.vue')['default']
    ConversationItem: typeof import('./src/components/chat/ConversationItem.vue')['default']
    ConversationList: typeof import('./src/components/chat/ConversationList.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElLoading: typeof import('element-plus/es')['ElLoading']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    EmptyState: typeof import('./src/components/chat/EmptyState.vue')['default']
    ErrorBoundary: typeof import('./src/components/common/ErrorBoundary.vue')['default']
    FilePreview: typeof import('./src/components/chat/FilePreview.vue')['default']
    ImageGenerator: typeof import('./src/components/image/ImageGenerator.vue')['default']
    ImageModelSelector: typeof import('./src/components/image/ImageModelSelector.vue')['default']
    Layout: typeof import('./src/components/common/Layout.vue')['default']
    MessageItem: typeof import('./src/components/chat/MessageItem.vue')['default']
    MessageList: typeof import('./src/components/chat/MessageList.vue')['default']
    ModelAvatarSimple: typeof import('./src/components/chat/ModelAvatarSimple.vue')['default']
    ModelSelector: typeof import('./src/components/chat/ModelSelector.vue')['default']
    NotificationDrawer: typeof import('./src/components/common/NotificationDrawer.vue')['default']
    QuickCommands: typeof import('./src/components/chat/QuickCommands.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
